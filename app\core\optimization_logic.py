#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化处理逻辑模块
提供OptimizationView的业务逻辑处理
"""

from typing import List, Tuple

from PySide6.QtCore import QObject, Signal

from app.core.app_manager import get_app_manager


class OptimizationLogic(QObject):
    """优化功能逻辑处理器

    负责处理系统优化相关任务的执行逻辑，包括电源选项解锁、注册表优化和系统清理。
    该类封装了与AppManager的交互，通过信号机制向UI层通知任务状态变化。

    Signals:
        taskStarted (str): 任务开始信号，参数为任务名称
        taskFinished (str, bool): 任务完成信号，参数为任务名称和是否成功
        taskProgress (str, int, int): 任务进度信号，参数为任务名称、当前进度和总进度
        allTasksFinished (int, int): 所有任务完成信号，参数为成功任务数和总任务数
        executionError (str): 执行错误信号，参数为错误信息
    """

    # 信号定义
    taskStarted = Signal(str)  # 任务开始
    taskFinished = Signal(str, bool)  # 任务完成，参数：任务名称，是否成功
    taskProgress = Signal(str, int, int)  # 任务进度，参数：任务名、当前进度和总进度
    allTasksFinished = Signal(int, int)  # 所有任务结束，参数：成功任务数和总任务数
    executionError = Signal(str)  # 执行错误，参数：错误信息字符串

    def __init__(self, parent=None):
        """初始化优化逻辑处理器

        Args:
            parent: 父对象，通常是OptimizationView实例
        """
        super().__init__(parent)
        self.app_manager = get_app_manager()

        # 连接应用管理器的信号
        self.app_manager.taskStarted.connect(self.taskStarted)
        self.app_manager.taskFinished.connect(self.taskFinished)
        self.app_manager.taskProgress.connect(self.taskProgress)
        self.app_manager.allTasksFinished.connect(self.allTasksFinished)

    def execute_tasks(self, task_type: str, selected_tasks: List) -> None:
        """执行优化任务

        根据任务类型，执行相应的优化操作，并通过信号机制向UI层通知执行状态。

        Args:
            task_type: 任务类型，可选值：'power', 'registry', 'cleanup'
            selected_tasks: 选中的任务列表
                - 如果是power类型，为选中的任务名称列表
                - 如果是registry类型，为(任务名称, 命令列表)元组的列表
                - 如果是cleanup类型，为(任务名称, 命令)元组的列表

        Raises:
            Exception: 执行任务时发生的异常会被捕获并通过executionError信号发送
        """
        if not selected_tasks:
            self.executionError.emit("未选择任务")
            return

        try:
            if task_type == "power":
                # 调用应用管理器执行解锁电源高级选项任务
                success = self.app_manager.execute_power_option_unlock()

                # 手动更新进度
                self.taskStarted.emit("解锁电源高级选项")
                self.taskFinished.emit("解锁电源高级选项", success)
                self.allTasksFinished.emit(1 if success else 0, 1)
            elif task_type == "registry":
                # 调用应用管理器执行注册表优化任务
                self.app_manager.execute_registry_tasks(selected_tasks)
            elif task_type == "cleanup":
                # 调用应用管理器执行系统清理任务
                self.app_manager.execute_cleanup_tasks(selected_tasks)
            else:
                self.executionError.emit(f"未知的任务类型: {task_type}")
        except Exception as e:
            self.executionError.emit(f"执行任务时出错: {str(e)}")
