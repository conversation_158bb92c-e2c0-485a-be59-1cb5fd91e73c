#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QStackedWidget,
    QCheckBox,
    QScrollArea,
)
from qfluentwidgets import (
    SegmentedWidget,
    SmoothScrollArea,
    SubtitleLabel,
    BodyLabel,
    FluentIcon as FIF,
    CardWidget,
    PrimaryPushButton,
    PushButton,
    CheckBox,
    InfoBar,
    InfoBarPosition,
    MessageBox,
)
import os
import json
import sys
from typing import Dict, List, Any, Tuple, Optional

from app.core.app_manager import get_app_manager
from app.core.dialog_manager import get_dialog_manager
from app.core.app_removal_logic import AppRemovalLogic
from app.components.common import set_checkboxes_state, update_selectall_state
from config.appx_packages import appx_packages
from config.onedrive_cleanup import ONEDRIVE_CLEANUP_TASKS


class AppRemovalView(QWidget):
    """预装应用视图"""

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("appRemovalView")

        # 获取应用管理器和对话框管理器
        self.app_manager = get_app_manager()
        self.dialog_manager = get_dialog_manager()

        # 创建逻辑处理器
        self.logic = AppRemovalLogic(self)

        # 连接逻辑处理器的信号
        self.logic.taskStarted.connect(self.onTaskStarted)
        self.logic.taskFinished.connect(self.onTaskFinished)
        self.logic.allTasksFinished.connect(self.onAllTasksFinished)
        self.logic.executionError.connect(self.onExecutionError)

        # 创建顶层布局
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(16, 16, 16, 16)
        self.vBoxLayout.setSpacing(12)

        # 创建标题和描述
        self.titleLabel = SubtitleLabel("预装应用", self)
        self.descriptionLabel = BodyLabel(
            "卸载Windows预装应用和清理OneDrive，释放系统空间", self
        )
        self.vBoxLayout.addWidget(self.titleLabel)
        self.vBoxLayout.addWidget(self.descriptionLabel)
        self.vBoxLayout.addSpacing(12)

        # 添加顶部操作按钮
        topActionLayout = QHBoxLayout()
        self.selectAllCheckbox = CheckBox("全选", self)
        self.selectAllCheckbox.clicked.connect(self.onSelectAllClicked)

        self.uninstallButton = PrimaryPushButton("卸载选中项")
        self.uninstallButton.setFixedWidth(150)
        self.uninstallButton.clicked.connect(self.onUninstallButtonClicked)

        topActionLayout.addWidget(self.selectAllCheckbox)
        topActionLayout.addStretch(1)
        topActionLayout.addWidget(self.uninstallButton)
        self.vBoxLayout.addLayout(topActionLayout)
        self.vBoxLayout.addSpacing(10)

        # 创建堆叠小部件
        self.stackedWidget = QStackedWidget(self)

        # 创建各个页面
        self.windowsAppPage = QWidget(self)
        self.windowsAppPage.setObjectName("windowsAppPage")
        self.onedrivePage = QWidget(self)
        self.onedrivePage.setObjectName("onedrivePage")

        # 为各页面设置布局
        self.windowsAppLayout = QVBoxLayout(self.windowsAppPage)
        self.windowsAppLayout.setContentsMargins(0, 0, 0, 0)
        self.windowsAppLayout.setSpacing(16)

        self.onedriveLayout = QVBoxLayout(self.onedrivePage)
        self.onedriveLayout.setContentsMargins(0, 0, 0, 0)
        self.onedriveLayout.setSpacing(16)

        # 添加页面到堆叠小部件
        self.stackedWidget.addWidget(self.windowsAppPage)
        self.stackedWidget.addWidget(self.onedrivePage)

        # 创建分段控件
        self.segmentedWidget = SegmentedWidget(self)
        self.segmentedWidget.addItem(
            routeKey="windowsAppPage",
            text="卸载Windows应用",
            onClick=lambda: self.switchPage(self.windowsAppPage),
        )
        self.segmentedWidget.addItem(
            routeKey="onedrivePage",
            text="卸载OneDrive",
            onClick=lambda: self.switchPage(self.onedrivePage),
        )

        # 设置默认选中的选项卡
        self.segmentedWidget.setCurrentItem("windowsAppPage")

        # 将分段控件和堆叠小部件添加到布局
        self.vBoxLayout.addWidget(self.segmentedWidget, 0, Qt.AlignLeft)
        self.vBoxLayout.addWidget(self.stackedWidget)

        self.initUI()

    def initUI(self):
        """初始化界面"""
        # 初始化Windows应用页面
        self.initWindowsAppPage()

        # 初始化OneDrive页面
        self.initOnedrivePage()

    def initWindowsAppPage(self):
        """初始化Windows应用页面"""
        # 创建滚动区域
        scrollArea = SmoothScrollArea()
        scrollArea.setWidgetResizable(True)
        scrollArea.setStyleSheet("background: transparent;")

        # 创建内容小部件
        contentWidget = QWidget()
        scrollLayout = QVBoxLayout(contentWidget)
        scrollLayout.setContentsMargins(0, 0, 0, 0)
        scrollLayout.setSpacing(16)
        scrollLayout.setAlignment(Qt.AlignTop)

        # 存储所有复选框的引用
        self.app_checkboxes = []

        # 为每个分类创建卡片
        for category, apps in appx_packages.items():
            card = CardWidget(self)
            cardLayout = QVBoxLayout(card)
            cardLayout.setContentsMargins(16, 16, 16, 16)
            cardLayout.setSpacing(10)

            # 添加分类标题
            titleLabel = SubtitleLabel(category, card)
            cardLayout.addWidget(titleLabel)

            # 添加每个应用的复选框
            # 如果应用是列表类型（针对测试数据）
            if isinstance(apps, list):
                for app in apps:
                    checkLayout = QHBoxLayout()
                    checkLayout.setContentsMargins(8, 0, 0, 0)

                    checkbox = CheckBox(app["name"], card)
                    checkbox.clicked.connect(self.updateSelectAllCheckbox)

                    checkLayout.addWidget(checkbox)
                    cardLayout.addLayout(checkLayout)

                    # 存储复选框引用
                    self.app_checkboxes.append((app["key"], checkbox))
            # 如果应用是字典类型（针对实际配置文件）
            elif isinstance(apps, dict):
                for app_name, app_pattern in apps.items():
                    checkLayout = QHBoxLayout()
                    checkLayout.setContentsMargins(8, 0, 0, 0)

                    checkbox = CheckBox(app_name, card)
                    checkbox.clicked.connect(self.updateSelectAllCheckbox)

                    checkLayout.addWidget(checkbox)
                    cardLayout.addLayout(checkLayout)

                    # 存储复选框引用（使用模式作为键）
                    self.app_checkboxes.append((app_pattern, checkbox))

            # 添加卡片到滚动区域
            scrollLayout.addWidget(card)

        # 设置滚动区域的内容
        scrollArea.setWidget(contentWidget)

        # 添加到页面布局
        self.windowsAppLayout.addWidget(scrollArea)

    def initOnedrivePage(self):
        """初始化OneDrive页面"""
        # 创建滚动区域
        scrollArea = SmoothScrollArea()
        scrollArea.setWidgetResizable(True)
        scrollArea.setStyleSheet("background: transparent;")

        # 创建内容小部件
        contentWidget = QWidget()
        scrollLayout = QVBoxLayout(contentWidget)
        scrollLayout.setContentsMargins(0, 0, 0, 0)
        scrollLayout.setSpacing(16)
        scrollLayout.setAlignment(Qt.AlignTop)

        # 创建清理任务卡片
        card = CardWidget(self)
        cardLayout = QVBoxLayout(card)
        cardLayout.setContentsMargins(16, 16, 16, 16)
        cardLayout.setSpacing(10)

        # 添加卡片标题
        titleLabel = SubtitleLabel("OneDrive 清理步骤", card)
        cardLayout.addWidget(titleLabel)

        # 添加描述
        descriptionLabel = BodyLabel(
            "卸载 OneDrive 并清理相关文件和注册表项。以下步骤将按顺序执行。",
            card,
        )
        descriptionLabel.setWordWrap(True)
        cardLayout.addWidget(descriptionLabel)

        # 存储所有复选框的引用
        self.onedrive_checkboxes = []

        # 添加每个清理步骤的复选框
        for task in ONEDRIVE_CLEANUP_TASKS:
            checkLayout = QHBoxLayout()
            checkLayout.setContentsMargins(8, 0, 0, 0)

            checkbox = CheckBox(task["name"], card)
            checkbox.clicked.connect(self.updateSelectAllCheckbox)

            checkLayout.addWidget(checkbox)
            cardLayout.addLayout(checkLayout)

            # 存储复选框引用
            task_key = task.get("key", task.get("name", ""))
            self.onedrive_checkboxes.append((task_key, checkbox))

        # 添加警告标签
        warningLabel = BodyLabel(
            "警告：卸载 OneDrive 可能会导致某些文件无法同步，请确保已备份重要文件。",
            card,
        )
        warningLabel.setWordWrap(True)
        warningLabel.setStyleSheet("color: red;")
        cardLayout.addWidget(warningLabel)

        # 添加卡片到滚动区域
        scrollLayout.addWidget(card)

        # 设置滚动区域的内容
        scrollArea.setWidget(contentWidget)

        # 添加到页面布局
        self.onedriveLayout.addWidget(scrollArea)

    def onSelectAllClicked(self):
        """处理全选复选框点击事件"""
        is_checked = self.selectAllCheckbox.isChecked()

        # 使用通用方法设置所有复选框状态
        set_checkboxes_state(
            [self.app_checkboxes, self.onedrive_checkboxes], is_checked
        )

    def updateSelectAllCheckbox(self):
        """更新全选复选框状态"""
        # 使用通用方法更新全选复选框状态
        update_selectall_state(
            self.selectAllCheckbox, [self.app_checkboxes, self.onedrive_checkboxes]
        )

    def onUninstallButtonClicked(self):
        """处理卸载按钮点击事件"""
        currentPage = self.stackedWidget.currentWidget()

        if currentPage == self.windowsAppPage:
            # 获取选中的应用
            selected_apps = []
            for key, checkbox in self.app_checkboxes:
                if checkbox.isChecked():
                    selected_apps.append((key, checkbox.text()))

            if not selected_apps:
                InfoBar.warning(
                    title="未选择应用",
                    content="请先选择要卸载的应用",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=3000,
                    parent=self,
                )
                return

            # 显示确认对话框
            app_names = [name for _, name in selected_apps]
            confirm_text = (
                "确定要卸载以下应用吗？这将删除所有用户的应用数据。\n\n"
                + "\n".join([f"• {name}" for name in app_names])
            )

            def on_confirm_uninstall():
                # 禁用卸载按钮，防止重复点击
                self.uninstallButton.setEnabled(False)
                # 调用逻辑处理器执行卸载
                self.logic.execute_uninstall(selected_apps, "app")

                InfoBar.info(
                    title="正在卸载",
                    content=f"正在卸载 {len(selected_apps)} 个应用，请稍候...",
                    orient=Qt.Horizontal,
                    isClosable=False,
                    position=InfoBarPosition.TOP,
                    duration=3000,
                    parent=self,
                )

            # 显示确认对话框
            self.dialog_manager.show_confirmation_dialog(
                title="确认卸载",
                content=confirm_text,
                on_confirm=on_confirm_uninstall,
                parent=self,
            )

        elif currentPage == self.onedrivePage:
            # 获取选中的OneDrive清理任务
            selected_tasks = []
            for key, checkbox in self.onedrive_checkboxes:
                if checkbox.isChecked():
                    selected_tasks.append((key, checkbox.text()))

            if not selected_tasks:
                InfoBar.warning(
                    title="未选择任务",
                    content="请先选择要执行的OneDrive清理任务",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=3000,
                    parent=self,
                )
                return

            # 显示确认对话框
            task_names = [name for _, name in selected_tasks]
            confirm_text = "确定要执行以下OneDrive清理任务吗？\n\n" + "\n".join(
                [f"• {name}" for name in task_names]
            )

            def on_confirm_cleanup():
                # 禁用卸载按钮，防止重复点击
                self.uninstallButton.setEnabled(False)
                # 调用逻辑处理器执行清理任务
                self.logic.execute_uninstall(selected_tasks, "onedrive")

                # 显示正在执行的提示
                InfoBar.info(
                    title="正在执行",
                    content=f"正在执行 {len(selected_tasks)} 个清理任务，请稍候...",
                    orient=Qt.Horizontal,
                    isClosable=False,
                    position=InfoBarPosition.TOP,
                    duration=3000,
                    parent=self,
                )

            # 显示确认对话框
            self.dialog_manager.show_confirmation_dialog(
                title="确认OneDrive清理",
                content=confirm_text,
                on_confirm=on_confirm_cleanup,
                parent=self,
            )

    def onExecutionError(self, error_msg: str):
        """处理执行错误"""
        # 确保在发生异常时重新启用按钮
        self.uninstallButton.setEnabled(True)

        # 显示错误信息
        InfoBar.error(
            title="执行错误",
            content=error_msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.BOTTOM_RIGHT,
            duration=3000,
            parent=self,
        )

    def onTaskStarted(self, task_name):
        """任务开始的回调"""
        InfoBar.info(
            title="正在执行",
            content=f"正在执行: {task_name}",
            orient=Qt.Horizontal,
            isClosable=False,
            position=InfoBarPosition.BOTTOM_RIGHT,
            duration=1000,
            parent=self,
        )

    def onTaskFinished(self, task_name, success):
        """任务完成的回调"""
        if success:
            InfoBar.success(
                title="任务完成",
                content=f"已完成: {task_name}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.BOTTOM_RIGHT,
                duration=1500,
                parent=self,
            )
        else:
            InfoBar.error(
                title="任务失败",
                content=f"失败: {task_name}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.BOTTOM_RIGHT,
                duration=3000,
                parent=self,
            )

    def onAllTasksFinished(self, success_count, total_count):
        """所有任务完成的回调"""
        # 重新启用卸载按钮
        self.uninstallButton.setEnabled(True)

        if success_count == total_count:
            InfoBar.success(
                title="全部完成",
                content=f"所有任务已成功完成 ({success_count}/{total_count})",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self,
            )
        else:
            InfoBar.warning(
                title="部分完成",
                content=f"部分任务执行失败 (成功: {success_count}/{total_count})",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=5000,
                parent=self,
            )

    def switchPage(self, page):
        """切换页面并更新全选复选框状态"""
        self.stackedWidget.setCurrentWidget(page)
        self.updateSelectAllCheckbox()
