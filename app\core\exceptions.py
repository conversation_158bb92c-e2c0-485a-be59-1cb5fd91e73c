#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统一异常处理模块
定义应用程序的自定义异常类和错误处理机制
"""

import traceback
from typing import Optional, Any, Callable
from functools import wraps

from PySide6.QtCore import QObject, Signal


class BaseAppException(Exception):
    """应用程序基础异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 details: Optional[str] = None):
        """初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 详细信息
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details
        
    def __str__(self) -> str:
        """返回异常的字符串表示"""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class HardwareException(BaseAppException):
    """硬件相关异常"""
    pass


class CommandExecutionException(BaseAppException):
    """命令执行异常"""
    pass


class PermissionException(BaseAppException):
    """权限相关异常"""
    pass


class ResourceException(BaseAppException):
    """资源相关异常"""
    pass


class ConfigurationException(BaseAppException):
    """配置相关异常"""
    pass


class TaskExecutionError(BaseAppException):
    """任务执行错误"""
    
    def __init__(self, task_name: str, message: str, error_code: Optional[str] = None):
        """初始化任务执行错误
        
        Args:
            task_name: 任务名称
            message: 错误消息
            error_code: 错误代码
        """
        super().__init__(message, error_code)
        self.task_name = task_name


class ErrorHandler(QObject):
    """错误处理器
    
    提供统一的错误处理和报告机制
    """
    
    # 信号定义
    errorOccurred = Signal(str, str)  # 错误发生信号 (错误类型, 错误消息)
    criticalErrorOccurred = Signal(str, str)  # 严重错误信号
    
    def __init__(self):
        super().__init__()
        self._error_handlers = {}
        
    def register_handler(self, exception_type: type, handler: Callable):
        """注册异常处理器
        
        Args:
            exception_type: 异常类型
            handler: 处理函数
        """
        self._error_handlers[exception_type] = handler
        
    def handle_exception(self, exception: Exception, context: str = "") -> bool:
        """处理异常
        
        Args:
            exception: 异常对象
            context: 上下文信息
            
        Returns:
            bool: 是否成功处理
        """
        try:
            exception_type = type(exception)
            
            # 查找特定的处理器
            if exception_type in self._error_handlers:
                return self._error_handlers[exception_type](exception, context)
            
            # 查找基类处理器
            for exc_type, handler in self._error_handlers.items():
                if issubclass(exception_type, exc_type):
                    return handler(exception, context)
            
            # 默认处理
            return self._default_handler(exception, context)
            
        except Exception as e:
            print(f"错误处理器本身出现异常: {e}")
            return False
            
    def _default_handler(self, exception: Exception, context: str) -> bool:
        """默认异常处理器
        
        Args:
            exception: 异常对象
            context: 上下文信息
            
        Returns:
            bool: 是否成功处理
        """
        error_msg = str(exception)
        error_type = type(exception).__name__
        
        # 发送错误信号
        if isinstance(exception, (PermissionException, CommandExecutionException)):
            self.criticalErrorOccurred.emit(error_type, error_msg)
        else:
            self.errorOccurred.emit(error_type, error_msg)
            
        # 打印错误信息（开发阶段）
        print(f"[{error_type}] {error_msg}")
        if context:
            print(f"上下文: {context}")
            
        return True


# 全局错误处理器实例
_error_handler = None


def get_error_handler() -> ErrorHandler:
    """获取全局错误处理器实例
    
    Returns:
        ErrorHandler: 错误处理器实例
    """
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler()
    return _error_handler


def handle_exceptions(context: str = "", reraise: bool = False):
    """异常处理装饰器
    
    Args:
        context: 上下文信息
        reraise: 是否重新抛出异常
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_handler = get_error_handler()
                handled = error_handler.handle_exception(e, context or func.__name__)
                
                if reraise or not handled:
                    raise
                    
                return None
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return: Any = None, 
                context: str = "", **kwargs) -> Any:
    """安全执行函数
    
    Args:
        func: 要执行的函数
        *args: 位置参数
        default_return: 异常时的默认返回值
        context: 上下文信息
        **kwargs: 关键字参数
        
    Returns:
        Any: 函数返回值或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        error_handler = get_error_handler()
        error_handler.handle_exception(e, context or func.__name__)
        return default_return


def retry_on_exception(max_retries: int = 3, delay: float = 1.0, 
                      exceptions: tuple = (Exception,)):
    """重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试延迟（秒）
        exceptions: 需要重试的异常类型
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            import time
            
            last_exception = None
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        print(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败，{delay}秒后重试...")
                        time.sleep(delay)
                    else:
                        print(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败")
                        
            # 重新抛出最后一个异常
            if last_exception:
                raise last_exception
                
        return wrapper
    return decorator
