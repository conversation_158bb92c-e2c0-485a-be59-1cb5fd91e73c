#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
启动画面组件
提供应用启动时的加载界面和进度指示
"""

import os
from typing import Optional

from PySide6.QtWidgets import QSplashScreen, QLabel, QVBoxLayout, QWidget, QProgressBar
from PySide6.QtCore import Qt, QTimer, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QPixmap, QPainter, QFont, QColor, QLinearGradient
from qfluentwidgets import FluentIcon as FIF


class ModernSplashScreen(QSplashScreen):
    """现代化启动画面
    
    提供美观的启动界面，包含应用图标、名称、版本信息和加载进度
    """
    
    # 信号定义
    messageChanged = Signal(str)  # 消息变更信号
    
    def __init__(self, width: int = 500, height: int = 300):
        """初始化启动画面
        
        Args:
            width: 启动画面宽度
            height: 启动画面高度
        """
        # 创建启动画面背景
        pixmap = self._create_background(width, height)
        super().__init__(pixmap)
        
        # 设置窗口属性
        self.setWindowFlags(
            Qt.WindowType.WindowStaysOnTopHint | 
            Qt.WindowType.FramelessWindowHint |
            Qt.WindowType.SplashScreen
        )
        
        # 初始化UI组件
        self._init_ui()
        
        # 设置样式
        self._setup_style()
        
    def _create_background(self, width: int, height: int) -> QPixmap:
        """创建启动画面背景
        
        Args:
            width: 背景宽度
            height: 背景高度
            
        Returns:
            QPixmap: 背景图像
        """
        pixmap = QPixmap(width, height)
        pixmap.fill(QColor(255, 255, 255, 240))  # 半透明白色背景
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 创建渐变背景
        gradient = QLinearGradient(0, 0, 0, height)
        gradient.setColorAt(0, QColor(240, 248, 255))  # 淡蓝色
        gradient.setColorAt(1, QColor(255, 255, 255))  # 白色
        
        painter.fillRect(pixmap.rect(), gradient)
        
        # 绘制边框
        painter.setPen(QColor(200, 200, 200))
        painter.drawRect(pixmap.rect().adjusted(0, 0, -1, -1))
        
        painter.end()
        return pixmap
        
    def _init_ui(self):
        """初始化UI组件"""
        # 创建主容器
        self.container = QWidget(self)
        self.container.setGeometry(0, 0, self.width(), self.height())
        
        # 创建布局
        layout = QVBoxLayout(self.container)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(20)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 应用标题
        self.title_label = QLabel("Windows 硬件工具箱", self.container)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.title_label)
        
        # 版本信息
        self.version_label = QLabel("v0.1.0", self.container)
        self.version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.version_label)
        
        # 添加弹性空间
        layout.addStretch(1)
        
        # 状态消息
        self.message_label = QLabel("正在初始化...", self.container)
        self.message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.message_label)
        
        # 进度条
        self.progress_bar = QProgressBar(self.container)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        self.progress_bar.setVisible(False)  # 默认隐藏
        layout.addWidget(self.progress_bar)
        
        # 版权信息
        self.copyright_label = QLabel("© 2025 All Rights Reserved", self.container)
        self.copyright_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.copyright_label)
        
    def _setup_style(self):
        """设置样式"""
        # 标题样式
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        self.title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
        
        # 版本信息样式
        version_font = QFont()
        version_font.setPointSize(10)
        self.version_label.setFont(version_font)
        self.version_label.setStyleSheet("color: #7f8c8d;")
        
        # 消息样式
        message_font = QFont()
        message_font.setPointSize(11)
        self.message_label.setFont(message_font)
        self.message_label.setStyleSheet("color: #34495e; margin: 5px;")
        
        # 版权信息样式
        copyright_font = QFont()
        copyright_font.setPointSize(8)
        self.copyright_label.setFont(copyright_font)
        self.copyright_label.setStyleSheet("color: #95a5a6;")
        
        # 进度条样式
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #ecf0f1;
                height: 8px;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 4px;
            }
        """)
        
    def showMessage(self, message: str, alignment: Qt.AlignmentFlag = Qt.AlignmentFlag.AlignCenter, 
                   color: QColor = QColor(52, 73, 94)):
        """显示消息
        
        Args:
            message: 要显示的消息
            alignment: 对齐方式
            color: 文字颜色
        """
        self.message_label.setText(message)
        self.messageChanged.emit(message)
        
        # 强制刷新界面
        self.repaint()
        
    def showProgress(self, show: bool = True):
        """显示或隐藏进度条
        
        Args:
            show: 是否显示进度条
        """
        self.progress_bar.setVisible(show)
        
    def setProgress(self, value: int, maximum: int = 100):
        """设置进度值
        
        Args:
            value: 当前进度值
            maximum: 最大进度值
        """
        self.progress_bar.setRange(0, maximum)
        self.progress_bar.setValue(value)
        
    def setIndeterminateProgress(self, enabled: bool = True):
        """设置不确定进度模式
        
        Args:
            enabled: 是否启用不确定进度模式
        """
        if enabled:
            self.progress_bar.setRange(0, 0)
        else:
            self.progress_bar.setRange(0, 100)


class SimpleSplashScreen(QSplashScreen):
    """简单启动画面
    
    为资源受限环境提供的简化版启动画面
    """
    
    def __init__(self):
        """初始化简单启动画面"""
        # 创建简单的背景
        pixmap = QPixmap(400, 200)
        pixmap.fill(QColor(255, 255, 255))
        super().__init__(pixmap)
        
        # 设置窗口属性
        self.setWindowFlags(
            Qt.WindowType.WindowStaysOnTopHint | 
            Qt.WindowType.FramelessWindowHint
        )
        
    def showMessage(self, message: str, alignment: Qt.AlignmentFlag = Qt.AlignmentFlag.AlignCenter,
                   color: QColor = QColor(0, 0, 0)):
        """显示消息"""
        super().showMessage(
            message, 
            alignment | Qt.AlignmentFlag.AlignBottom, 
            color
        )


def create_splash_screen(modern: bool = True) -> QSplashScreen:
    """创建启动画面
    
    Args:
        modern: 是否使用现代化启动画面
        
    Returns:
        QSplashScreen: 启动画面实例
    """
    if modern:
        return ModernSplashScreen()
    else:
        return SimpleSplashScreen()
