#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用卸载逻辑模块
提供AppRemovalView的业务逻辑处理
"""

from typing import List, Tuple, Dict, Any

from PySide6.QtCore import QObject, Signal

from app.core.app_manager import get_app_manager
from config.onedrive_cleanup import ONEDRIVE_CLEANUP_TASKS


class AppRemovalLogic(QObject):
    """应用卸载逻辑处理器
    
    负责处理预装应用卸载和OneDrive清理的业务逻辑，
    通过信号机制向UI层通知任务状态变化。
    
    Signals:
        taskStarted (str): 任务开始信号，参数为任务名称
        taskFinished (str, bool): 任务完成信号，参数为任务名称和是否成功
        allTasksFinished (int, int): 所有任务完成信号，参数为成功任务数和总任务数
        executionError (str): 执行错误信号，参数为错误信息
    """

    # 信号定义
    taskStarted = Signal(str)  # 任务开始
    taskFinished = Signal(str, bool)  # 任务完成，参数：任务名称，是否成功
    allTasksFinished = Signal(int, int)  # 所有任务结束，参数：成功任务数和总任务数
    executionError = Signal(str)  # 执行错误，参数：错误信息字符串

    def __init__(self, parent=None):
        """初始化应用卸载逻辑处理器
        
        Args:
            parent: 父对象，通常是AppRemovalView实例
        """
        super().__init__(parent)
        self.app_manager = get_app_manager()

        # 连接应用管理器的信号
        self.app_manager.taskStarted.connect(self.taskStarted)
        self.app_manager.taskFinished.connect(self.taskFinished)
        self.app_manager.allTasksFinished.connect(self.allTasksFinished)

    def execute_uninstall(
        self, selected_items: List[Tuple[str, str]], page_type: str
    ) -> None:
        """执行卸载任务
        
        根据页面类型，执行应用卸载或OneDrive清理操作，
        并通过信号机制向UI层通知执行状态。

        Args:
            selected_items: 选中的项目列表，每项为 (key, display_name) 元组
            page_type: 页面类型，'app' 为Windows应用，'onedrive' 为OneDrive清理
            
        Raises:
            Exception: 执行任务时发生的异常会被捕获并通过executionError信号发送
        """
        if not selected_items:
            self.executionError.emit("未选择任何项目")
            return

        try:
            if page_type == "app":
                # 获取应用模式列表（用于卸载）
                app_patterns = [key for key, _ in selected_items]

                # 执行卸载操作
                self.app_manager.execute_appx_uninstall(app_patterns)
            elif page_type == "onedrive":
                # 准备要执行的任务列表
                cleanup_tasks = []
                for task_key, task_name in selected_items:
                    # 从配置中查找对应的命令
                    for task in ONEDRIVE_CLEANUP_TASKS:
                        if task.get("key", task.get("name", "")) == task_key:
                            if "command" in task:
                                cleanup_tasks.append((task["name"], task["command"]))
                            elif "commands" in task:
                                cleanup_tasks.append((task["name"], task["commands"]))
                            break

                # 执行清理任务
                if cleanup_tasks:
                    self.app_manager.execute_onedrive_cleanup(cleanup_tasks)
                else:
                    self.executionError.emit("未找到任何可执行的清理任务")
            else:
                self.executionError.emit(f"未知的页面类型: {page_type}")
        except Exception as e:
            self.executionError.emit(f"执行卸载任务时出错: {str(e)}")
