#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
import traceback

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QLocale, QTimer
from qfluentwidgets import FluentTranslator

# 导入硬件管理器
from app.core import get_hardware_manager

# 导入主窗口
from app.main_window import MainWindow

# 导入启动画面
from app.utils.splash_screen import create_splash_screen

# 导入异常处理
from app.core.exceptions import get_error_handler, handle_exceptions


@handle_exceptions("应用程序启动")
def initialize_application():
    """初始化应用程序"""
    # 创建应用实例
    app = QApplication(sys.argv)

    # 安装翻译器
    translator = FluentTranslator(QLocale(QLocale.Chinese, QLocale.China))
    app.installTranslator(translator)

    # 禁用native siblings的属性，避免与Qt的frameless window产生冲突
    app.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)

    return app


@handle_exceptions("硬件信息初始化")
def initialize_hardware_manager():
    """初始化硬件管理器"""
    hardware_manager = get_hardware_manager()
    # 异步获取硬件信息，不阻塞启动
    hardware_manager.fetch_hardware_info()
    return hardware_manager


def main():
    """主函数"""
    try:
        # 初始化应用程序
        app = initialize_application()
        if not app:
            return 1

        # 创建启动画面
        splash = create_splash_screen(modern=True)
        splash.show()
        splash.showMessage("正在初始化应用...")
        app.processEvents()

        # 初始化错误处理器
        error_handler = get_error_handler()

        # 更新启动画面
        splash.showMessage("正在加载组件...")
        app.processEvents()

        # 创建主窗口
        window = MainWindow()

        # 更新启动画面
        splash.showMessage("正在获取硬件信息...")
        app.processEvents()

        # 初始化硬件管理器（异步）
        hardware_manager = initialize_hardware_manager()

        # 更新启动画面
        splash.showMessage("正在完成初始化...")
        app.processEvents()

        # 显示主窗口
        window.show()

        # 输出窗口显示后的实际大小
        from config.app_config import (
            DEBUG_WINDOW_SIZE,
            WINDOW_DEFAULT_WIDTH,
            WINDOW_DEFAULT_HEIGHT,
        )

        if DEBUG_WINDOW_SIZE:
            print(f"显示后实际窗口大小: {window.width()}x{window.height()}")

        # 在窗口显示后重新设置大小（解决FluentWindow内部调整的问题）
        window.resize(WINDOW_DEFAULT_WIDTH, WINDOW_DEFAULT_HEIGHT)

        if DEBUG_WINDOW_SIZE:
            print(f"重新设置后窗口大小: {window.width()}x{window.height()}")

        # 关闭启动画面
        splash.finish(window)

        # 运行应用事件循环
        return app.exec()

    except Exception as e:
        print(f"程序启动失败: {e}")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
