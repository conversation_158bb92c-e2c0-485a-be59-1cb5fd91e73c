#!/usr/bin/env python3
# -*- coding: utf-8 -*-

execution_policy = "Set-ExecutionPolicy Bypass -Scope LocalMachine -Force"
unlock_power_options = "Get-ChildItem 'HKLM:\\SYSTEM\\CurrentControlSet\\Control\\Power\\PowerSettings' -Recurse | Where-Object {$_.PSPath -notmatch '\\\\DefaultPowerSchemeValues|(\\\\[0-9]|\\\\255)$'} | ForEach-Object {Set-ItemProperty -Path $_.PSPath -Name 'Attributes' -Value 2 -Force}"
