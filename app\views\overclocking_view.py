#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtCore import Qt, QSize
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel
from PySide6.QtGui import QIcon, QPixmap
from qfluentwidgets import (
    SmoothScrollArea,
    CardWidget,
    SubtitleLabel,
    BodyLabel,
    PrimaryPushButton,
    PushButton,
    IconWidget,
    FluentIcon as FIF,
    InfoBar,
    InfoBarPosition,
)
import os
import sys

from app.utils.icon_extractor import get_icon_extractor
from app.core.command_runner import get_command_runner, CommandType, CommandResult
from app.core.dialog_manager import get_dialog_manager


class OverclockingView(QWidget):
    """超频工具视图"""

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("overclockingView")

        # 获取命令运行器和对话框管理器
        self.command_runner = get_command_runner()
        self.dialog_manager = get_dialog_manager()
        
        # 连接命令运行器的信号
        self.command_runner.commandFinished.connect(self.onCommandFinished)

        # 存储异步任务，防止被回收
        self._async_tasks = []

        # 创建顶层布局
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(16, 16, 16, 16)
        self.vBoxLayout.setSpacing(12)

        # 创建标题和描述
        self.titleLabel = SubtitleLabel("超频工具", self)
        self.descriptionLabel = BodyLabel(
            "提供一键进入BIOS功能和常用超频工具的快速启动", self
        )
        self.vBoxLayout.addWidget(self.titleLabel)
        self.vBoxLayout.addWidget(self.descriptionLabel)
        self.vBoxLayout.addSpacing(12)

        # 创建"一键进入BIOS"卡片
        self.biosCard = CardWidget(self)
        self.biosCardLayout = QVBoxLayout(self.biosCard)
        self.biosCardLayout.setContentsMargins(16, 16, 16, 16)
        self.biosCardLayout.setSpacing(10)

        # 添加BIOS卡片标题和说明
        self.biosCardTitle = SubtitleLabel("一键进入BIOS", self.biosCard)
        self.biosCardLayout.addWidget(self.biosCardTitle)

        self.biosCardDescription = BodyLabel(
            "重启计算机并直接进入BIOS设置。使用此功能可以快速访问超频、内存设置等高级选项，无需手动按键盘快捷键。",
            self.biosCard,
        )
        self.biosCardDescription.setWordWrap(True)
        self.biosCardLayout.addWidget(self.biosCardDescription)

        # 添加"进入BIOS"按钮
        self.enterBiosButton = PrimaryPushButton("进入BIOS", self.biosCard)
        self.enterBiosButton.setFixedWidth(150)
        self.enterBiosButton.setIcon(FIF.POWER_BUTTON)
        self.enterBiosButton.clicked.connect(self.onEnterBiosClicked)
        self.biosCardLayout.addWidget(self.enterBiosButton, 0, Qt.AlignRight)

        # 将BIOS卡片添加到主布局
        self.vBoxLayout.addWidget(self.biosCard)
        self.vBoxLayout.addSpacing(16)

        # 创建工具列表标题
        self.toolsTitle = SubtitleLabel("超频工具列表", self)
        self.vBoxLayout.addWidget(self.toolsTitle)
        self.vBoxLayout.addSpacing(8)

        # 创建滚动区域
        self.scrollArea = SmoothScrollArea(self)
        self.scrollWidget = QWidget(self)
        self.scrollArea.setWidget(self.scrollWidget)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setStyleSheet("background: transparent;")

        # 工具列表的布局
        self.toolsLayout = QVBoxLayout(self.scrollWidget)
        self.toolsLayout.setContentsMargins(0, 0, 0, 0)
        self.toolsLayout.setSpacing(8)  # 减小间距从12减到8
        self.toolsLayout.setAlignment(Qt.AlignTop)

        # 将滚动区域添加到主布局
        self.vBoxLayout.addWidget(self.scrollArea)

        # 初始化界面
        self.initUI()
        
        # 存储当前操作的执行文件名，用于异步回调
        self._current_exe_name = ""

    def initUI(self):
        """初始化界面"""
        # 扫描OCTools目录获取超频工具
        self.scanOCTools()

    def scanOCTools(self):
        """扫描OCTools目录获取超频工具"""
        oc_tools_dir = "OCTools"

        # 检查目录是否存在
        if not os.path.exists(oc_tools_dir) or not os.path.isdir(oc_tools_dir):
            # 显示提示信息
            emptyLabel = BodyLabel(
                "未找到OCTools目录。请确保该目录存在并包含超频工具。", self
            )
            emptyLabel.setWordWrap(True)
            self.toolsLayout.addWidget(emptyLabel)
            return

        # 扫描目录
        tools_added = False
        icon_extractor = get_icon_extractor()

        try:
            # 获取目录下的所有子文件夹
            subdirs = [
                d
                for d in os.listdir(oc_tools_dir)
                if os.path.isdir(os.path.join(oc_tools_dir, d))
            ]

            for subdir in sorted(subdirs):
                tool_path = os.path.join(oc_tools_dir, subdir)

                # 查找可执行文件
                exe_files = []
                for file in os.listdir(tool_path):
                    if file.lower().endswith(".exe"):
                        exe_files.append(file)

                # 如果没有找到可执行文件，则跳过
                if not exe_files:
                    continue

                # 优先使用与文件夹同名的exe文件
                main_exe = None
                for exe in exe_files:
                    if exe.lower() == subdir.lower() + ".exe":
                        main_exe = exe
                        break

                # 如果没有找到与文件夹同名的exe，则使用第一个exe文件
                if main_exe is None and exe_files:
                    main_exe = exe_files[0]

                if main_exe:
                    exe_path = os.path.join(tool_path, main_exe)

                    # 创建工具卡片
                    toolCard = CardWidget(self)
                    toolCard.setFixedHeight(70)  # 设置标准卡片高度
                    cardLayout = QHBoxLayout(toolCard)
                    cardLayout.setContentsMargins(16, 16, 16, 16)  # 增加边距
                    cardLayout.setSpacing(12)

                    # 创建图标
                    iconLabel = QLabel(toolCard)
                    if os.path.exists(exe_path) and exe_path.lower().endswith(".exe"):
                        # 提取可执行文件的图标
                        icon = icon_extractor.get_file_icon(exe_path)
                        if not icon.isNull():
                            pixmap = icon_extractor.get_best_icon_pixmap(
                                icon, QSize(32, 32)
                            )
                            iconLabel.setPixmap(pixmap)
                        else:
                            # 使用默认图标
                            icon = QIcon(FIF.SPEED_HIGH.path)
                            pixmap = icon.pixmap(32, 32)
                            iconLabel.setPixmap(pixmap)
                    else:
                        # 使用默认图标
                        icon = QIcon(FIF.SPEED_HIGH.path)
                        pixmap = icon.pixmap(32, 32)
                        iconLabel.setPixmap(pixmap)

                    iconLabel.setFixedSize(32, 32)
                    cardLayout.addWidget(iconLabel)

                    # 添加工具名称
                    nameLabel = BodyLabel(subdir, toolCard)
                    nameLabel.setObjectName("toolName")
                    # 使用样式表设置字体大小
                    nameLabel.setStyleSheet("font-size: 11pt; font-weight: bold;")
                    cardLayout.addWidget(nameLabel)

                    # 添加伸缩项
                    cardLayout.addStretch(1)

                    # 添加启动按钮
                    launchButton = PrimaryPushButton("启动", toolCard)
                    launchButton.setFixedWidth(80)
                    launchButton.setIcon(FIF.PLAY)
                    launchButton.clicked.connect(
                        lambda checked=False, path=exe_path: self.launchTool(path)
                    )
                    cardLayout.addWidget(launchButton)

                    # 添加到布局
                    self.toolsLayout.addWidget(toolCard)
                    tools_added = True

            # 如果没有添加任何工具，显示提示信息
            if not tools_added:
                emptyLabel = BodyLabel("OCTools目录中未找到可用的超频工具。", self)
                self.toolsLayout.addWidget(emptyLabel)

        except Exception as e:
            errorLabel = BodyLabel(f"扫描OCTools目录时出错: {str(e)}", self)
            errorLabel.setWordWrap(True)
            self.toolsLayout.addWidget(errorLabel)

    def launchTool(self, exe_path):
        """启动超频工具

        Args:
            exe_path: 可执行文件路径
        """
        # 获取文件名作为显示名称
        exe_name = os.path.basename(exe_path)

        try:
            # 检查文件是否存在
            if not os.path.exists(exe_path):
                InfoBar.error(
                    title="启动失败",
                    content=f"文件不存在: {exe_path}",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=3000,
                    parent=self,
                )
                return

            # 直接执行启动，不再需要确认
            self.execute_launch(exe_path, exe_name)

        except Exception as e:
            InfoBar.error(
                title="启动失败",
                content=f"启动工具时出错: {str(e)}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self,
            )

    def execute_launch(self, exe_path, exe_name):
        """执行启动工具

        Args:
            exe_path: 可执行文件路径
            exe_name: 可执行文件名
        """
        try:
            # 显示启动中提示
            InfoBar.info(
                title="启动中",
                content=f"正在启动 {exe_name}，请稍候...",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=2000,
                parent=self,
            )

            # 使用子进程直接启动，避免线程问题
            import subprocess
            import sys

            if sys.platform == "win32":
                # 设置当前操作的执行文件名
                self._current_exe_name = exe_name

                # 使用命令运行器异步执行
                self.command_runner.execute_command(
                    command=exe_path,
                    command_type=CommandType.EXECUTABLE,
                    admin=True,
                    async_mode=True,  # 改为异步执行
                )
            else:
                # 非Windows系统
                InfoBar.warning(
                    title="不支持",
                    content="超频工具仅支持Windows系统",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=3000,
                    parent=self,
                )

        except Exception as e:
            InfoBar.error(
                title="启动失败",
                content=f"启动 {exe_name} 时出错: {str(e)}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self,
            )

    def onEnterBiosClicked(self):
        """处理进入BIOS按钮点击事件"""
        try:
            # 确认进入BIOS
            self.dialog_manager.show_confirmation_dialog(
                title="进入BIOS",
                content="这将重启您的计算机并直接进入BIOS设置界面。\n请确保已保存所有工作。\n\n是否继续？",
                on_confirm=self.enterBios,
                parent=self,
            )
        except Exception as e:
            InfoBar.error(
                title="操作失败",
                content=f"准备进入BIOS时出错: {str(e)}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self,
            )

    def enterBios(self):
        """进入BIOS"""
        try:
            # 显示操作中提示
            InfoBar.info(
                title="操作中",
                content="正在重启并进入BIOS，请稍候...",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=2000,
                parent=self,
            )

            # 设置当前操作的执行文件名
            self._current_exe_name = "BIOS进入"

            # 使用命令运行器异步执行命令
            self.command_runner.execute_command(
                command="shutdown /r /fw /t 0",
                command_type=CommandType.CMD,
                admin=True,
                async_mode=True,  # 改为异步执行
            )

        except Exception as e:
            InfoBar.error(
                title="操作失败",
                content=f"进入BIOS时出错: {str(e)}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self,
            )

    def onCommandFinished(self, result: CommandResult) -> None:
        """命令执行完成的回调
        
        Args:
            result: 命令执行结果
        """
        # 获取当前操作的执行文件名
        exe_name = self._current_exe_name
        
        if result.success:
            # 执行成功
            if exe_name == "BIOS进入":
                # 对于进入BIOS操作，通常系统会立即重启，不会执行到这里
                pass
            else:
                # 显示成功消息
                InfoBar.success(
                    title="启动成功",
                    content=f"{exe_name} 已成功启动",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=2000,
                    parent=self,
                )
        else:
            # 执行失败
            error_msg = result.error if hasattr(result, "error") else "未知错误"
            InfoBar.error(
                title="操作失败",
                content=f"操作 {exe_name} 失败: {error_msg}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self,
            )
