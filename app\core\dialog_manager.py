#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
对话框管理
提供创建和管理对话框的功能
"""

from typing import Callable, Optional, Any
from PySide6.QtWidgets import QWidget
from qfluentwidgets import MessageBox


class DialogManager:
    """对话框管理器"""

    def __init__(self):
        """初始化对话框管理器"""
        pass

    def show_confirmation_dialog(
        self,
        title: str,
        content: str,
        on_confirm: Optional[Callable] = None,
        on_cancel: Optional[Callable] = None,
        parent: Optional[QWidget] = None,
    ) -> None:
        """显示确认对话框

        Args:
            title: 对话框标题
            content: 对话框内容
            on_confirm: 确认按钮点击回调
            on_cancel: 取消按钮点击回调
            parent: 父控件
        """
        # 创建消息框
        message_box = MessageBox(title, content, parent=parent)

        # 如果有回调函数，连接信号
        if on_confirm:
            message_box.yesSignal.connect(on_confirm)
        if on_cancel:
            message_box.cancelSignal.connect(on_cancel)

        # 显示对话框
        message_box.exec()

    def show_info_dialog(
        self,
        title: str,
        content: str,
        on_close: Optional[Callable] = None,
        parent: Optional[QWidget] = None,
    ) -> None:
        """显示信息对话框

        Args:
            title: 对话框标题
            content: 对话框内容
            on_close: 关闭按钮点击回调
            parent: 父控件
        """
        # 创建消息框
        message_box = MessageBox(title, content, parent=parent)
        message_box.cancelButton.hide()  # 隐藏取消按钮
        message_box.yesButton.setText("确定")  # 修改确认按钮文本

        # 如果有回调函数，连接信号
        if on_close:
            message_box.yesSignal.connect(on_close)

        # 显示对话框
        message_box.exec()

    def show_error_dialog(
        self,
        title: str,
        content: str,
        on_close: Optional[Callable] = None,
        parent: Optional[QWidget] = None,
    ) -> None:
        """显示错误对话框

        Args:
            title: 对话框标题
            content: 对话框内容
            on_close: 关闭按钮点击回调
            parent: 父控件
        """
        # 创建消息框
        message_box = MessageBox(title, content, parent=parent)
        message_box.cancelButton.hide()  # 隐藏取消按钮
        message_box.yesButton.setText("确定")  # 修改确认按钮文本

        # 设置错误样式
        message_box.setIcon(MessageBox.Icon.ERROR)

        # 如果有回调函数，连接信号
        if on_close:
            message_box.yesSignal.connect(on_close)

        # 显示对话框
        message_box.exec()


# 单例模式
_dialog_manager_instance = None


def get_dialog_manager() -> DialogManager:
    """获取DialogManager实例(单例模式)

    Returns:
        DialogManager: 对话框管理器实例
    """
    global _dialog_manager_instance
    if _dialog_manager_instance is None:
        _dialog_manager_instance = DialogManager()
    return _dialog_manager_instance
