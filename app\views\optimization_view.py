#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QStackedWidget,
    QCheckBox,
    QScrollArea,
    QDialog,
)
from qfluentwidgets import (
    SegmentedWidget,
    SmoothScrollArea,
    SubtitleLabel,
    BodyLabel,
    FluentIcon as FIF,
    CardWidget,
    PrimaryPushButton,
    PushButton,
    CheckBox,
    InfoBar,
    InfoBarPosition,
    ProgressRing,
)
import os
import json
import sys
import subprocess
import threading
import time

from app.core.app_manager import get_app_manager
from app.core.dialog_manager import get_dialog_manager
from app.core.optimization_logic import OptimizationLogic
from app.utils.async_task import AsyncTask
from app.components.common import set_checkboxes_state, update_selectall_state
from config import registry_commands, system_cleanup


class OptimizationView(QWidget):
    """优化清理视图"""

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("optimizationView")

        # 获取应用管理器和对话框管理器
        self.app_manager = get_app_manager()
        self.dialog_manager = get_dialog_manager()

        # 创建逻辑处理器
        self.logic = OptimizationLogic(self)

        # 连接逻辑处理器的信号
        self.logic.taskStarted.connect(self.onTaskStarted)
        self.logic.taskFinished.connect(self.onTaskFinished)
        self.logic.taskProgress.connect(self.onTaskProgress)
        self.logic.allTasksFinished.connect(self.onAllTasksFinished)
        self.logic.executionError.connect(self.onExecutionError)

        # 创建顶层布局
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(16, 16, 16, 16)
        self.vBoxLayout.setSpacing(12)

        # 创建标题和描述
        self.titleLabel = SubtitleLabel("优化清理", self)
        self.descriptionLabel = BodyLabel(
            "提供电源优化、注册表优化和系统清理功能，可帮助提升系统性能", self
        )
        self.vBoxLayout.addWidget(self.titleLabel)
        self.vBoxLayout.addWidget(self.descriptionLabel)
        self.vBoxLayout.addSpacing(12)

        # 添加顶部操作按钮
        topActionLayout = QHBoxLayout()
        self.selectAllCheckbox = CheckBox("全选", self)
        self.selectAllCheckbox.clicked.connect(self.onSelectAllClicked)

        self.executeButton = PrimaryPushButton("执行选中任务")
        self.executeButton.setFixedWidth(150)
        self.executeButton.clicked.connect(self.onExecuteButtonClicked)

        topActionLayout.addWidget(self.selectAllCheckbox)
        topActionLayout.addStretch(1)
        topActionLayout.addWidget(self.executeButton)
        self.vBoxLayout.addLayout(topActionLayout)
        self.vBoxLayout.addSpacing(10)

        # 创建进度指示器布局
        self.progressLayout = QHBoxLayout()
        self.progressLayout.setContentsMargins(0, 0, 0, 0)
        self.progressLayout.setAlignment(Qt.AlignCenter)

        self.progressRing = ProgressRing(self)
        self.progressRing.setFixedSize(50, 50)
        self.progressRing.hide()
        self.progressLayout.addWidget(self.progressRing)

        self.vBoxLayout.addLayout(self.progressLayout)

        # 创建堆叠小部件
        self.stackedWidget = QStackedWidget(self)

        # 创建各个页面
        self.powerOptionsPage = QWidget(self)
        self.powerOptionsPage.setObjectName("powerOptionsPage")
        self.registryOptimizationPage = QWidget(self)
        self.registryOptimizationPage.setObjectName("registryOptimizationPage")
        self.systemCleanupPage = QWidget(self)
        self.systemCleanupPage.setObjectName("systemCleanupPage")

        # 为各页面设置布局
        self.powerOptionsLayout = QVBoxLayout(self.powerOptionsPage)
        self.powerOptionsLayout.setContentsMargins(0, 0, 0, 0)
        self.powerOptionsLayout.setSpacing(16)

        self.registryOptimizationLayout = QVBoxLayout(self.registryOptimizationPage)
        self.registryOptimizationLayout.setContentsMargins(0, 0, 0, 0)
        self.registryOptimizationLayout.setSpacing(16)

        self.systemCleanupLayout = QVBoxLayout(self.systemCleanupPage)
        self.systemCleanupLayout.setContentsMargins(0, 0, 0, 0)
        self.systemCleanupLayout.setSpacing(16)

        # 添加页面到堆叠小部件
        self.stackedWidget.addWidget(self.powerOptionsPage)
        self.stackedWidget.addWidget(self.registryOptimizationPage)
        self.stackedWidget.addWidget(self.systemCleanupPage)

        # 创建分段控件
        self.segmentedWidget = SegmentedWidget(self)
        self.segmentedWidget.addItem(
            routeKey="powerOptionsPage",
            text="电源选项",
            onClick=lambda: self.switchPage(self.powerOptionsPage),
        )
        self.segmentedWidget.addItem(
            routeKey="registryOptimizationPage",
            text="注册表优化",
            onClick=lambda: self.switchPage(self.registryOptimizationPage),
        )
        self.segmentedWidget.addItem(
            routeKey="systemCleanupPage",
            text="系统清理",
            onClick=lambda: self.switchPage(self.systemCleanupPage),
        )

        # 设置默认选中的选项卡
        self.segmentedWidget.setCurrentItem("powerOptionsPage")

        # 将分段控件和堆叠小部件添加到布局
        self.vBoxLayout.addWidget(self.segmentedWidget, 0, Qt.AlignLeft)
        self.vBoxLayout.addWidget(self.stackedWidget)

        self.initUI()

    def initUI(self):
        """初始化界面"""
        # 初始化电源选项页面
        self.initPowerOptionsPage()

        # 初始化注册表优化页面
        self.initRegistryOptimizationPage()

        # 初始化系统清理页面
        self.initSystemCleanupPage()

    def initPowerOptionsPage(self):
        """初始化电源选项页面"""
        # 创建滚动区域
        scrollArea = SmoothScrollArea()
        scrollArea.setWidgetResizable(True)
        scrollArea.setStyleSheet("background: transparent;")

        # 创建内容小部件
        contentWidget = QWidget()
        scrollLayout = QVBoxLayout(contentWidget)
        scrollLayout.setContentsMargins(0, 0, 0, 0)
        scrollLayout.setSpacing(16)
        scrollLayout.setAlignment(Qt.AlignTop)

        # 创建电源选项卡片
        powerCard = CardWidget(self)
        powerCardLayout = QVBoxLayout(powerCard)
        powerCardLayout.setContentsMargins(16, 16, 16, 16)
        powerCardLayout.setSpacing(10)

        # 添加标题
        titleLabel = SubtitleLabel("电源高级选项", powerCard)
        powerCardLayout.addWidget(titleLabel)

        # 添加复选框并存储到列表中
        self.power_checkboxes = []
        unlockCheckbox = CheckBox("解锁电源高级选项", powerCard)
        unlockCheckbox.setToolTip(
            "Windows默认情况下隐藏了部分高级电源选项。此功能可解锁这些隐藏选项，"
            "让您能够更精细地控制系统电源管理，包括处理器电源管理、硬盘高级电源设置等。"
            "解锁后，您可以在系统控制面板的电源选项中查看这些设置。"
        )
        unlockCheckbox.stateChanged.connect(self.updateSelectAllCheckbox)
        powerCardLayout.addWidget(unlockCheckbox)

        # 添加复选框到列表
        self.power_checkboxes.append(("解锁电源高级选项", unlockCheckbox))

        # 添加卡片到滚动区域
        scrollLayout.addWidget(powerCard)
        scrollLayout.addStretch(1)

        # 设置滚动区域的内容
        scrollArea.setWidget(contentWidget)

        # 添加到页面布局
        self.powerOptionsLayout.addWidget(scrollArea)

    def initRegistryOptimizationPage(self):
        """初始化注册表优化页面"""
        # 创建滚动区域
        scrollArea = SmoothScrollArea()
        scrollArea.setWidgetResizable(True)
        scrollArea.setStyleSheet("background: transparent;")

        # 创建内容小部件
        contentWidget = QWidget()
        scrollLayout = QVBoxLayout(contentWidget)
        scrollLayout.setContentsMargins(0, 0, 0, 0)
        scrollLayout.setSpacing(16)
        scrollLayout.setAlignment(Qt.AlignTop)

        try:
            # 从配置文件加载注册表命令
            self.registry_commands = registry_commands.registry_commands
            self.registry_checkboxes = []

            # 为每个分类创建卡片
            for category, commands in self.registry_commands.items():
                # 创建卡片
                card = CardWidget(contentWidget)
                cardLayout = QVBoxLayout(card)
                cardLayout.setContentsMargins(16, 16, 16, 16)
                cardLayout.setSpacing(10)

                # 添加卡片标题
                titleLabel = SubtitleLabel(category, card)
                cardLayout.addWidget(titleLabel)

                # 为每个命令创建复选框
                for command_name in commands:
                    checkbox = CheckBox(command_name, card)
                    checkbox.stateChanged.connect(self.updateSelectAllCheckbox)
                    cardLayout.addWidget(checkbox)

                    # 添加到复选框列表，用于后续处理
                    self.registry_checkboxes.append((command_name, checkbox))

                # 添加卡片到布局
                scrollLayout.addWidget(card)

        except Exception as e:
            # 发生异常时显示错误
            errorLabel = BodyLabel(f"加载注册表命令失败: {str(e)}", contentWidget)
            scrollLayout.addWidget(errorLabel)

        # 设置滚动区域的内容
        scrollArea.setWidget(contentWidget)

        # 添加到页面布局
        self.registryOptimizationLayout.addWidget(scrollArea)

    def initSystemCleanupPage(self):
        """初始化系统清理页面"""
        # 创建滚动区域
        scrollArea = SmoothScrollArea()
        scrollArea.setWidgetResizable(True)
        scrollArea.setStyleSheet("background: transparent;")

        # 创建内容小部件
        contentWidget = QWidget()
        scrollLayout = QVBoxLayout(contentWidget)
        scrollLayout.setContentsMargins(0, 0, 0, 0)
        scrollLayout.setSpacing(16)
        scrollLayout.setAlignment(Qt.AlignTop)

        try:
            # 从配置文件加载系统清理命令
            self.system_cleanup_tasks = system_cleanup.SYSTEM_CLEANUP_TASKS
            self.cleanup_checkboxes = []

            # 为每个分类创建卡片
            for category, tasks in self.system_cleanup_tasks.items():
                # 创建卡片
                card = CardWidget(contentWidget)
                cardLayout = QVBoxLayout(card)
                cardLayout.setContentsMargins(16, 16, 16, 16)
                cardLayout.setSpacing(10)

                # 添加卡片标题
                titleLabel = SubtitleLabel(category, card)
                cardLayout.addWidget(titleLabel)

                # 为每个任务创建复选框
                for task in tasks:
                    checkbox = CheckBox(task["name"], card)
                    checkbox.stateChanged.connect(self.updateSelectAllCheckbox)
                    cardLayout.addWidget(checkbox)

                    # 添加到复选框列表，用于后续处理
                    self.cleanup_checkboxes.append((task["name"], checkbox))

                # 添加卡片到布局
                scrollLayout.addWidget(card)

        except Exception as e:
            # 发生异常时显示错误
            errorLabel = BodyLabel(f"加载系统清理任务失败: {str(e)}", contentWidget)
            scrollLayout.addWidget(errorLabel)

        # 设置滚动区域的内容
        scrollArea.setWidget(contentWidget)

        # 添加到页面布局
        self.systemCleanupLayout.addWidget(scrollArea)

    def onSelectAllClicked(self):
        """处理全选复选框点击事件"""
        is_checked = self.selectAllCheckbox.isChecked()

        # 使用通用方法设置所有复选框状态
        set_checkboxes_state(
            [self.power_checkboxes, self.registry_checkboxes, self.cleanup_checkboxes],
            is_checked,
        )

    def updateSelectAllCheckbox(self):
        """更新全选复选框状态"""
        # 使用通用方法更新全选复选框状态
        update_selectall_state(
            self.selectAllCheckbox,
            [self.power_checkboxes, self.registry_checkboxes, self.cleanup_checkboxes],
        )

    def onExecuteButtonClicked(self):
        """处理执行按钮点击事件"""
        currentPage = self.stackedWidget.currentWidget()

        if currentPage == self.powerOptionsPage:
            # 获取选中的电源选项
            selected_tasks = []
            for key, checkbox in self.power_checkboxes:
                if checkbox.isChecked():
                    selected_tasks.append(key)

            if selected_tasks:

                def on_confirm():
                    self.progressRing.show()
                    self.executeButton.setEnabled(False)
                    # 调用逻辑处理器执行任务
                    self.logic.execute_tasks("power", selected_tasks)

                self.dialog_manager.show_confirmation_dialog(
                    title="执行电源优化",
                    content=f"您选择了{len(selected_tasks)}项电源优化任务。\n这些操作将修改系统注册表以解锁所有隐藏的电源高级选项。\n操作完成后，您可以在控制面板的电源选项中查看这些设置。\n\n是否继续？",
                    on_confirm=on_confirm,
                    parent=self,
                )
            else:
                InfoBar.warning(
                    title="未选择任务",
                    content="请至少选择一项电源优化任务",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=2000,
                    parent=self,
                )

        elif currentPage == self.registryOptimizationPage:
            # 获取选中的注册表优化项
            selected_tasks = []
            for key, checkbox in self.registry_checkboxes:
                if checkbox.isChecked():
                    command = self.registry_commands
                    for category, commands in command.items():
                        if key in commands:
                            selected_tasks.append((key, commands[key]))
                            break

            if selected_tasks:

                def on_confirm():
                    self.progressRing.show()
                    self.executeButton.setEnabled(False)
                    # 调用逻辑处理器执行任务
                    self.logic.execute_tasks("registry", selected_tasks)

                self.dialog_manager.show_confirmation_dialog(
                    title="执行注册表优化",
                    content=f"您选择了{len(selected_tasks)}项注册表优化任务，执行后将修改系统注册表。\n操作完成后可能需要重启资源管理器以应用更改。\n\n是否继续？",
                    on_confirm=on_confirm,
                    parent=self,
                )
            else:
                InfoBar.warning(
                    title="未选择任务",
                    content="请至少选择一项注册表优化任务",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=2000,
                    parent=self,
                )

        elif currentPage == self.systemCleanupPage:
            # 获取选中的系统清理项
            selected_tasks = []
            for key, checkbox in self.cleanup_checkboxes:
                if checkbox.isChecked():
                    for category, tasks in self.system_cleanup_tasks.items():
                        for task in tasks:
                            if task["name"] == key:
                                if "command" in task:
                                    selected_tasks.append((key, task["command"]))
                                elif "commands" in task:
                                    # 如果有多个命令，只取第一个
                                    selected_tasks.append((key, task["commands"][0]))
                                break

            if selected_tasks:

                def on_confirm():
                    self.progressRing.show()
                    self.executeButton.setEnabled(False)
                    # 调用逻辑处理器执行任务
                    self.logic.execute_tasks("cleanup", selected_tasks)

                self.dialog_manager.show_confirmation_dialog(
                    title="执行系统清理",
                    content=f"您选择了{len(selected_tasks)}项系统清理任务，执行后将删除系统中的临时文件和缓存。\n\n是否继续？",
                    on_confirm=on_confirm,
                    parent=self,
                )
            else:
                InfoBar.warning(
                    title="未选择任务",
                    content="请至少选择一项系统清理任务",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP_RIGHT,
                    duration=2000,
                    parent=self,
                )

    def onExecutionError(self, error_msg: str):
        """处理执行错误"""
        # 重置UI状态
        self.progressRing.hide()
        self.executeButton.setEnabled(True)

        # 显示错误信息
        InfoBar.error(
            title="执行错误",
            content=error_msg,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self,
        )

    def onTaskStarted(self, task_name: str):
        """任务开始执行的回调"""
        InfoBar.info(
            title="正在执行",
            content=f"正在执行: {task_name}",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.BOTTOM,
            duration=1000,
            parent=self,
        )

    def onTaskFinished(self, task_name: str, is_success: bool):
        """任务完成的回调"""
        if is_success:
            InfoBar.success(
                title="任务完成",
                content=f"{task_name} 执行成功",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.BOTTOM,
                duration=1000,
                parent=self,
            )
        else:
            InfoBar.error(
                title="任务失败",
                content=f"{task_name} 执行失败",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.BOTTOM,
                duration=2000,
                parent=self,
            )

    def onTaskProgress(self, task_name: str, current: int, total: int):
        """任务进度的回调"""
        # 计算百分比
        percent = int((current / total) * 100) if total > 0 else 0

        # 更新进度环
        if hasattr(self, "progressRing"):
            self.progressRing.setTextVisible(True)
            self.progressRing.setValue(percent)
            self.progressRing.setFormat(f"{percent}% ({current}/{total})")

        # 更新状态栏
        InfoBar.info(
            title="任务进度",
            content=f"{task_name}: {percent}% ({current}/{total})",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.BOTTOM,
            duration=1000,
            parent=self,
        )

    def onAllTasksFinished(self, successful: int, total: int):
        """所有任务完成的回调"""
        self.progressRing.hide()
        self.executeButton.setEnabled(True)

        if successful == total:

            def on_restart_confirm():
                # 重启资源管理器
                self.restartExplorerAndDeleteIconCache()

            self.dialog_manager.show_confirmation_dialog(
                title="任务完成",
                content=f"所有{total}项任务已完成。\n\n是否需要重启资源管理器以应用更改？",
                on_confirm=on_restart_confirm,
                parent=self,
            )
        else:
            self.dialog_manager.show_info_dialog(
                title="任务部分完成",
                content=f"共{total}项任务，成功{successful}项，失败{total - successful}项。",
                parent=self,
            )

    def restartExplorerAndDeleteIconCache(self):
        """重启资源管理器并删除图标缓存"""
        try:
            # 首先关闭确认对话框
            for widget in self.window().findChildren(QDialog):
                if widget.isVisible():
                    widget.accept()

            # 显示正在处理的提示
            InfoBar.info(
                title="操作中",
                content="正在重启资源管理器，请稍候...",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=2000,
                parent=self,
            )

            # 使用分离进程执行资源管理器重启，避免阻塞UI
            def restart_in_thread():
                try:
                    # 终止资源管理器
                    subprocess.call("taskkill /f /im explorer.exe", shell=True)

                    # 短暂等待
                    time.sleep(1)

                    # 启动资源管理器
                    subprocess.Popen("explorer.exe", shell=True)

                    # 删除图标缓存
                    try:
                        # 尝试删除图标缓存文件
                        cache_dir = os.path.join(
                            os.environ.get("LOCALAPPDATA", ""),
                            "Microsoft\\Windows\\Explorer",
                        )
                        for file in os.listdir(cache_dir):
                            if file.startswith("iconcache"):
                                try:
                                    os.remove(os.path.join(cache_dir, file))
                                except:
                                    pass
                    except:
                        pass

                except Exception as e:
                    print(f"重启资源管理器出错: {e}")

            # 启动异步任务执行重启
            AsyncTask.run_async(restart_in_thread)

            # 显示成功消息
            InfoBar.success(
                title="操作已执行",
                content="重启资源管理器命令已发送，请稍候...",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=2000,
                parent=self,
            )

        except Exception as e:
            InfoBar.error(
                title="操作失败",
                content=f"重启资源管理器失败: {str(e)}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self,
            )

    def switchPage(self, page):
        """切换页面并更新全选复选框状态"""
        self.stackedWidget.setCurrentWidget(page)
        self.updateSelectAllCheckbox()
