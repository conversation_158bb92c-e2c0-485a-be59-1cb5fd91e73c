# 项目任务清单：Windows 硬件工具箱

---
## 第一阶段：项目初始化与基础框架搭建

- [x] **任务 1.1: 环境配置**
    - [x] 1.1.1: 创建 Python 虚拟环境。
    - [x] 1.1.2: 安装 `PySide6` 和 `PySide6-Fluent-Widgets`。
    - [x] 1.1.3: 将依赖项冻结到 `requirements.txt`。

- [x] **任务 1.2: 项目结构创建**
    - [x] 1.2.1: 创建主应用目录（如 `app`）。
    - [x] 1.2.2: 在 `app` 内创建 `views`, `components`, `core`, `utils` 等子目录。
    - [x] 1.2.3: 保持 `assets` 和 `config` 目录在项目根目录。

- [x] **任务 1.3: 主窗口与导航框架**
    - [x] 1.3.1: 在 `app` 中创建主窗口文件 `main_window.py`。
    - [x] 1.3.2: 使用 `FluentWindow` 作为主窗口基类。
    - [x] 1.3.3: 设置窗口标题和图标 (`assets/icon.ico`)。
    - [x] 1.3.4: 在 `views` 目录下为每个导航项创建临时的空白 `QWidget` 文件。
    - [x] 1.3.5: 在主窗口中，使用 `addSubInterface` 添加所有导航项及其对应的空白视图。
    - [x] 1.3.6: 创建 `main.py` 作为程序入口，实例化并显示主窗口。

- [x] **任务 1.4: 资源整理**
    - [x] 1.4.1: 从项目依赖的 `PySide6-Fluent-Widgets` 中导出 `FluentIcon` 名称。
    - [x] 1.4.2: 将图标名称列表保存到 `fluent_icons.txt` 文件中，方便后续查找和引用。获取图标列表命令：`python -c "from qfluentwidgets import FluentIcon as FIF; print('\n'.join(sorted([attr for attr in dir(FIF) if not attr.startswith('_')])))"`

---
## 第二阶段：核心功能模块开发 (UI)

- [x] **任务 2.1: 硬件信息视图**
    - [x] 2.1.1: 在 `views/hardware_info_view.py` 中创建 `SmoothScrollArea` 作为根容器。
    - [x] 2.1.2: 在顶部创建系统信息卡片（`FIF.HOME`），包含系统名称、版本、架构和主机名信息。
    - [x] 2.1.3: 在系统信息卡片下方创建垂直布局，用于放置按硬件类别划分的 `CardWidget`，包括：
        - [x] CPU信息卡片（`FIF.LABEL`）
        - [x] 主板信息卡片（`FIF.SETTING`）
        - [x] 内存信息卡片（`FIF.LIBRARY`）
        - [x] GPU信息卡片（`FIF.PALETTE`）
        - [x] 磁盘信息卡片（`FIF.FOLDER`）

- [x] **任务 2.2: 优化清理视图**
    - [x] 2.2.1: 在 `views/optimization_view.py` 中创建界面，并添加 `SegmentedWidget` 和 `QStackedWidget` 作为主布局。
    - [x] 2.2.2: 在视图顶部添加"执行选中任务"按钮和全局全选复选框，以及居中的 `ProgressRing`。全选功能可同时选择所有选项卡中的全部任务。
    - [x] 2.2.3: **电源选项卡**:
        - [x] 在此选项卡页面中，创建 `SmoothScrollArea` 并添加一个 `CardWidget` 用于"解锁电源高级选项"，包含复选框。
    - [x] 2.2.4: **注册表优化选项卡**:
        - [x] 在此选项卡页面中，创建 `SmoothScrollArea`，并通过顶部的全局全选复选框控制所有任务的选择状态。
        - [x] 读取 `config/registry_commands.py`，根据分类动态创建 `CardWidget` 列表，卡片内包含任务复选框。
    - [x] 2.2.5: **系统清理选项卡**:
        - [x] 在此选项卡页面中，创建 `SmoothScrollArea`，并实现与注册表优化页相同的布局和交互逻辑。
        - [x] 创建 `config/system_cleanup.py` 配置文件。
        - [x] 读取配置文件，动态创建 `CardWidget` 列表。

- [x] **任务 2.3: 预装应用视图**
    - [x] 2.3.1: 在 `views/app_removal_view.py` 中创建界面，并添加 `SegmentedWidget` 和 `QStackedWidget` 作为主布局。
    - [x] 2.3.2: 在视图顶部添加"卸载选中项"按钮和全局全选复选框。全选功能可同时选择所有选项卡中的全部应用。
    - [x] 2.3.3: **卸载Windows应用选项卡**:
        - [x] 在此选项卡页面中，创建 `SmoothScrollArea`，并通过顶部的全局全选复选框控制所有应用的选择状态。
        - [x] 读取`config/appx_packages.py`，根据分类动态创建 `CardWidget` 列表，卡片内包含应用复选框。
    - [x] 2.3.4: **卸载OneDrive选项卡**:
        - [x] 在此选项卡页面中，创建 `SmoothScrollArea`，并实现与卸载Windows应用页相同的布局和交互逻辑。
        - [x] 创建 `config/onedrive_cleanup.py` 配置文件。
        - [x] 读取配置文件，动态创建 `CardWidget` 列表。

- [x] **任务 2.4: 超频工具视图**
    - [x] 2.4.1: 在 `views/overclocking_view.py` 中创建界面主布局。
    - [x] 2.4.2: 在顶部创建一个独立的 `CardWidget` 用于"一键进入 BIOS"功能，包含说明和按钮。
    - [x] 2.4.3: 在下方创建 `SmoothScrollArea`，并根据 `OCTools` 目录动态创建 `CardWidget` 工具列表，每个列表项都是一个固定高度的卡片。
    - [x] 2.4.4: 每个工具卡片需采用横向布局，包含从 `.exe` 提取的图标、加粗的工具名和一个"启动"按钮。
    - [x] 2.4.5: **图标提取实现**:
        - [x] 2.4.5.1: 创建 `utils/icon_extractor.py` 工具类，用于提取文件图标。
        - [x] 2.4.5.2: 使用其中的方法从 `.exe` 文件中提取图标并获取最佳尺寸。
        - [x] 2.4.5.3: 仅提取 `.exe` 文件的图标，忽略其他类型文件。

- [x] **任务 2.5: 快捷工具视图**
    - [x] 2.5.1: 在 `views/quick_tools_view.py` 中创建 `SmoothScrollArea` 作为根容器。
    - [x] 2.5.2: 读取 `config/quick_tools.py`，为每个分类创建一个 `CardWidget`。
    - [x] 2.5.3: 为每个分类卡片添加带图标的标题。
    - [x] 2.5.4: 在卡片内容区实现 `FlowLayout`。
    - [x] 2.5.5: 创建"图标+文本"样式的 `CardWidget` 作为工具按钮，并添加到 `FlowLayout` 中。

- [x] **任务 2.6: 关于软件视图**
    - [x] 2.6.1: 在 `views/about_view.py` 中创建主布局。
    - [x] 2.6.2: 在顶部创建包含应用图标、名称、版本号和版权信息的横向布局。
    - [x] 2.6.3: 创建第一个 `SettingCardGroup`（操作组）。
        - [x] 添加"检查更新"的 `PushSettingCard`。
        - [x] 添加"赞助作者"的 `PushSettingCard`。
    - [x] 2.6.4: 创建第二个 `SettingCardGroup`（链接组）。
        - [x] 添加作者信息、QQ群、项目源码的 `HyperlinkCard`。
    - [x] 2.6.5: 为赞助功能实现继承于 `MessageBoxBase` 的弹窗，并使用 `SegmentedWidget` 切换二维码。

---
## 第三阶段：后端逻辑与功能实现

- [x] **任务 3.1: 核心执行引擎**
    - [x] 3.1.1: 在 `core/command_runner.py` 中创建用于执行外部命令的类/函数。
    - [x] 3.1.2: 实现以管理员权限运行命令的逻辑。
    - [x] 3.1.3: 实现 PowerShell 脚本的执行逻辑 (包括设置 `Bypass` 策略)。
    - [x] 3.1.4: 实现注册表命令的执行逻辑。
    - [x] 3.1.5: 在 `utils` 中添加检测 `.exe` 是 GUI 还是 CLI 的辅助函数。
    - [x] 3.1.6: 在 `utils` 中添加异步执行任务的装饰器或基类 (`QThread`)。

- [ ] **任务 3.2: 视图逻辑绑定**
    - [x] 3.2.1: **硬件信息**: 实现 `wmi`/`psutil` 的调用，并将信息填充到UI。
    - [x] 3.2.2: **优化清理**: 将UI操作连接到 `command_runner`，执行选中任务，并在完成后调用后处理逻辑（重启explorer, 删除iconcache）。
    - [x] 3.2.3: **预装应用**: 将UI操作连接到 `command_runner`，执行 `AppxPackage` 和 `AppxProvisionedPackage` 的卸载逻辑。
    - [x] 3.2.4: **超频工具**:
        - [x] 实现扫描 `OCTools` 目录和提取 `.exe` 图标的逻辑，并将结果填充到UI列表。
        - [x] 调用 `utils/icon_extractor.py` 中的方法提取和显示最佳尺寸的图标。
        - [x] 绑定"启动"按钮点击事件到 `command_runner`，实现优先启动同名 `.exe` 的逻辑。
        - [x] 绑定"一键进入BIOS"按钮点击事件。
    - [x] 3.2.5: **快捷工具**: 绑定按钮点击事件到 `command_runner`，并实现一次性安全模式逻辑。

- [x] **任务 3.3: 交互弹窗实现**
    - [x] 3.3.1: 封装一个通用的二次确认对话框函数。
    - [x] 3.3.2: 在所有需要确认的操作前调用该函数。
    - [x] 3.3.3: 实现赞助二维码的弹窗 (`Dialog`)。
    - [x] 3.3.4: 在关于应用的弹窗中，为赞助功能实现带有选项卡(`Pivot`)切换二维码的对话框。

---
## 第四阶段：测试、优化与文档

- [x] **任务 4.1: 全面功能测试**
    - [x] 4.1.1: 逐一测试每个按钮和复选框的功能。
    - [x] 4.1.2: 测试"全选"和"一键执行"等批量操作。
    - [x] 4.1.3: 在 Win10 和 Win11 系统上进行兼容性测试。
    - [x] 4.1.4: 测试图标提取功能，确保在不同系统上能正确获取最佳尺寸的图标。

- [x] **任务 4.2: 代码与性能优化**
    - [x] 4.2.1: 检查并重构重复的代码块。
    - [x] 4.2.2: 为所有可能失败的IO和系统调用添加 `try-except` 块。
    - [x] 4.2.3: 确保所有耗时操作都在子线程中执行，并提供状态反馈（如 `ProgressRing`）。

- [ ] **任务 4.3: 项目文档**
    - [x] 4.3.1: 创建并撰写 `README.md`。
    - [x] 4.3.2: 创建 `logs.md` 并记录第一个正式版本信息。
    - [x] 4.3.3: 检查代码注释是否清晰、完整。 