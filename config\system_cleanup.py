#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统清理配置
"""

# 系统清理任务列表
SYSTEM_CLEANUP_TASKS = {
    # 常见缓存文件
    "缓存文件": [
        {
            "name": "临时文件",
            "description": "删除Windows临时文件夹中的所有文件",
            "command": "Remove-Item -Path $env:TEMP\\* -Recurse -Force -ErrorAction SilentlyContinue",
        },
        {
            "name": "下载的程序文件",
            "description": "删除下载的程序文件缓存",
            "command": 'Remove-Item -Path "$env:SystemRoot\\Downloaded Program Files\\*" -Recurse -Force -ErrorAction SilentlyContinue',
        },
        {
            "name": "Windows更新临时文件",
            "description": "删除Windows更新临时文件",
            "command": 'Remove-Item -Path "$env:SystemRoot\\SoftwareDistribution\\Download\\*" -Recurse -Force -ErrorAction SilentlyContinue',
        },
    ],
    # 浏览器缓存
    "浏览器缓存": [
        {
            "name": "Edge浏览器缓存",
            "description": "删除Microsoft Edge浏览器缓存",
            "command": 'if (Test-Path "$env:LOCALAPPDATA\\Microsoft\\Edge\\User Data\\Default\\Cache") { Remove-Item -Path "$env:LOCALAPPDATA\\Microsoft\\Edge\\User Data\\Default\\Cache\\*" -Recurse -Force -ErrorAction SilentlyContinue }',
        },
        {
            "name": "Chrome浏览器缓存",
            "description": "删除Google Chrome浏览器缓存",
            "command": 'if (Test-Path "$env:LOCALAPPDATA\\Google\\Chrome\\User Data\\Default\\Cache") { Remove-Item -Path "$env:LOCALAPPDATA\\Google\\Chrome\\User Data\\Default\\Cache\\*" -Recurse -Force -ErrorAction SilentlyContinue }',
        },
    ],
    # 系统垃圾
    "系统垃圾": [
        {
            "name": "回收站",
            "description": "清空回收站",
            "command": "Clear-RecycleBin -Force -ErrorAction SilentlyContinue",
        },
        {
            "name": "缩略图缓存",
            "description": "删除Windows缩略图缓存数据库",
            "command": 'Remove-Item -Path "$env:LOCALAPPDATA\\Microsoft\\Windows\\Explorer\\thumbcache_*.db" -Force -ErrorAction SilentlyContinue',
        },
        {
            "name": "系统错误内存转储",
            "description": "删除Windows错误报告和系统内存转储",
            "command": 'Remove-Item -Path "$env:SystemRoot\\Minidump\\*" -Recurse -Force -ErrorAction SilentlyContinue; Remove-Item -Path "$env:SystemRoot\\memory.dmp" -Force -ErrorAction SilentlyContinue',
        },
    ],
    # 应用程序缓存
    "应用程序缓存": [
        {
            "name": "Windows商店缓存",
            "description": "删除Windows应用商店缓存",
            "command": 'Get-Process -Name "WSHost", "RuntimeBroker", "ApplicationFrameHost" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue; Start-Sleep -Seconds 1; Remove-Item -Path "$env:LOCALAPPDATA\\Packages\\Microsoft.WindowsStore_*\\LocalCache\\*" -Recurse -Force -ErrorAction SilentlyContinue; Remove-Item -Path "$env:LOCALAPPDATA\\Packages\\Microsoft.WindowsStore_*\\TempState\\*" -Recurse -Force -ErrorAction SilentlyContinue',
        },
        {
            "name": "应用程序预取",
            "description": "删除应用程序预取文件",
            "command": 'Remove-Item -Path "$env:SystemRoot\\Prefetch\\*" -Force -ErrorAction SilentlyContinue',
        },
        {
            "name": "字体缓存",
            "description": "删除并重建Windows字体缓存",
            "command": 'Stop-Service "FontCache" -Force -ErrorAction SilentlyContinue; Remove-Item -Path "$env:SystemRoot\\ServiceProfiles\\LocalService\\AppData\\Local\\FontCache\\*" -Force -ErrorAction SilentlyContinue; Start-Service "FontCache" -ErrorAction SilentlyContinue',
        },
    ],
}
