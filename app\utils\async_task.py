#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步任务工具
提供异步执行耗时任务的装饰器和基类
"""

import functools
import threading
import traceback
from typing import Any, Callable, Optional, TypeVar, cast, TYPE_CHECKING

from PySide6.QtCore import (
    QObject,
    QThread,
    Signal,
    QRunnable,
    QThreadPool,
    Slot,
    Qt,
    QMetaObject,
)


# 类型定义
T = TypeVar("T")
R = TypeVar("R")

if TYPE_CHECKING:
    from typing import Self


class AsyncTaskError(Exception):
    """异步任务异常基类"""

    pass


class TaskExecutionError(AsyncTaskError):
    """任务执行错误"""

    def __init__(self, task_name: str, original_error: Exception):
        self.task_name = task_name
        self.original_error = original_error
        super().__init__(f"任务 '{task_name}' 执行失败: {str(original_error)}")


class AsyncTask(QThread):
    """异步任务基类

    用于在单独的线程中执行耗时的任务，同时提供信号来通知任务进度和结果
    """

    # 信号定义
    taskStarted = Signal()
    taskFinished = Signal(object)  # 任务结果
    taskProgress = Signal(int, int)  # 当前进度，总进度
    taskError = Signal(str)  # 错误信息

    def __init__(self, parent=None, task_name: str = ""):
        super().__init__(parent)
        self._task_name = task_name or "未命名任务"
        self._func = None
        self._args = ()
        self._kwargs = {}
        self._result = None
        self._exception = None

    def set_task(self, func: Callable[..., T], *args, **kwargs) -> None:
        """设置要执行的任务

        Args:
            func: 要执行的函数
            *args: 位置参数
            **kwargs: 关键字参数
        """
        self._func = func
        self._args = args
        self._kwargs = kwargs
        self._result = None
        self._exception = None

    def run(self) -> None:
        """执行任务（由QThread调用）"""
        if not self._func:
            self.taskError.emit(f"任务未设置: {self._task_name}")
            return

        self.taskStarted.emit()

        try:
            self._result = self._func(*self._args, **self._kwargs)
            self.taskFinished.emit(self._result)
        except Exception as e:
            self._exception = e
            error_msg = f"任务异常: {str(e)}"
            traceback_str = traceback.format_exc()
            print(f"{error_msg}\n{traceback_str}")
            self.taskError.emit(error_msg)

    def result(self) -> Optional[T]:
        """获取任务结果

        Returns:
            任务结果，如果任务失败或尚未完成则返回None
        """
        return self._result

    def exception(self) -> Optional[Exception]:
        """获取任务异常

        Returns:
            任务异常，如果任务成功或尚未完成则返回None
        """
        return self._exception

    @staticmethod
    def run_async(
        func: Callable[..., T],
        *args,
        on_start: Optional[Callable[[], None]] = None,
        on_finish: Optional[Callable[[T], None]] = None,
        on_error: Optional[Callable[[str], None]] = None,
        task_name: str = "",
        **kwargs,
    ) -> "AsyncTask":
        """静态方法：异步执行任务

        Args:
            func: 要执行的函数
            *args: 位置参数
            on_start: 任务开始时的回调函数
            on_finish: 任务完成时的回调函数
            on_error: 任务出错时的回调函数
            task_name: 任务名称，用于标识和日志
            **kwargs: 关键字参数

        Returns:
            AsyncTask: 任务对象
        """
        if not task_name and hasattr(func, "__name__"):
            task_name = func.__name__

        task = AsyncTask(task_name=task_name)
        task.set_task(func, *args, **kwargs)

        if on_start:
            task.taskStarted.connect(on_start)
        if on_finish:
            task.taskFinished.connect(on_finish)
        if on_error:
            task.taskError.connect(on_error)

        task.start()
        return task


# 向前声明ThreadPoolTask类型
_ThreadPoolTask = TypeVar("_ThreadPoolTask", bound="ThreadPoolTask")


class ThreadPoolTask(QObject):
    """线程池任务

    使用QThreadPool执行任务，更轻量级，适合大量小型任务
    """

    # 信号定义
    taskStarted = Signal()
    taskFinished = Signal(object)  # 任务结果
    taskError = Signal(str)  # 错误信息

    def __init__(self, func: Callable[..., T], *args, task_name: str = "", **kwargs):
        super().__init__()
        self._func = func
        self._args = args
        self._kwargs = kwargs
        self._task_name = (
            task_name or (hasattr(func, "__name__") and func.__name__) or "未命名任务"
        )
        self._result = None
        self._exception = None

    def run(self) -> None:
        """执行任务"""
        self.taskStarted.emit()

        try:
            result = self._func(*self._args, **self._kwargs)
            self._result = result
            self.taskFinished.emit(result)
        except Exception as e:
            self._exception = e
            error_msg = f"线程池任务异常: {self._task_name} - {str(e)}"
            traceback_str = traceback.format_exc()
            print(f"{error_msg}\n{traceback_str}")
            self.taskError.emit(error_msg)

    def result(self) -> Optional[T]:
        """获取任务结果"""
        return self._result

    def exception(self) -> Optional[Exception]:
        """获取任务异常"""
        return self._exception

    @staticmethod
    def run_async(
        func: Callable[..., T],
        *args,
        on_start: Optional[Callable[[], None]] = None,
        on_finish: Optional[Callable[[T], None]] = None,
        on_error: Optional[Callable[[str], None]] = None,
        task_name: str = "",
        **kwargs,
    ) -> _ThreadPoolTask:
        """静态方法：异步执行任务

        Args:
            func: 要执行的函数
            *args: 位置参数
            on_start: 任务开始时的回调函数
            on_finish: 任务完成时的回调函数
            on_error: 任务出错时的回调函数
            task_name: 任务名称，用于标识和日志
            **kwargs: 关键字参数

        Returns:
            ThreadPoolTask: 任务对象
        """
        task = ThreadPoolTask(func, *args, task_name=task_name, **kwargs)

        if on_start:
            task.taskStarted.connect(on_start)
        if on_finish:
            task.taskFinished.connect(on_finish)
        if on_error:
            task.taskError.connect(on_error)

        # 创建可运行对象
        class Runnable(QRunnable):
            @Slot()
            def run(self):
                task.run()

        # 提交到线程池
        runnable = Runnable()
        QThreadPool.globalInstance().start(runnable)

        return task


def async_task(
    on_start: Optional[Callable[[], None]] = None,
    on_finish: Optional[Callable[[Any], None]] = None,
    on_error: Optional[Callable[[str], None]] = None,
    use_thread_pool: bool = False,
    task_name: str = "",
) -> Callable[[Callable[..., R]], Callable[..., None]]:
    """异步任务装饰器

    装饰一个函数，使其异步执行

    Args:
        on_start: 任务开始时的回调函数
        on_finish: 任务完成时的回调函数
        on_error: 任务出错时的回调函数
        use_thread_pool: 是否使用线程池
        task_name: 任务名称，为空则使用函数名

    Returns:
        Callable: 装饰后的函数
    """

    def decorator(func: Callable[..., R]) -> Callable[..., None]:
        # 如果未指定任务名，使用函数名
        _task_name = task_name or func.__name__

        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> None:
            if use_thread_pool:
                ThreadPoolTask.run_async(
                    func,
                    *args,
                    on_start=on_start,
                    on_finish=on_finish,
                    on_error=on_error,
                    task_name=_task_name,
                    **kwargs,
                )
            else:
                AsyncTask.run_async(
                    func,
                    *args,
                    on_start=on_start,
                    on_finish=on_finish,
                    on_error=on_error,
                    task_name=_task_name,
                    **kwargs,
                )

        return wrapper

    return decorator
