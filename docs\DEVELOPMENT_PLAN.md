
# 项目开发计划：Windows 硬件工具箱

## 1. 项目概述
本项目旨在使用 `PySide6` 和 `PySide6-Fluent-Widgets` 开发一款现代化的、功能丰富的 Windows 硬件工具箱。应用将遵循 Fluent Design 设计原则，提供包括硬件信息查看、系统优化、应用管理、超频和快捷工具在内的多项功能。

## 2. 开发阶段与里程碑

### 第一阶段：项目初始化与基础框架搭建 (预计1-2天)
- **里程碑**: 成功运行一个带有左侧导航和空白内容区域的基础窗口。
1.  **环境配置**:
    - 创建虚拟环境。
    - 安装必要的依赖库 (`PySide6`, `PySide6-Fluent-Widgets` 等) 并生成 `requirements.txt`。
2.  **项目结构设计**:
    - 创建清晰、可扩展的文件夹和文件结构（例如，`app`, `views`, `components`, `core`, `utils`, `assets`, `config`）。
3.  **主窗口实现**:
    - 创建基于 `FluentWindow` 的主窗口。
    - 初始化左侧导航栏，添加所有顶层导航项（硬件信息、优化清理等），并为每个导航项关联一个临时的空白占位符视图。
4.  **图标与资源**:
    - 设置应用程序的全局图标 (`assets/icon.ico`)。
    - 生成并保存 `fluent_icons.txt` 列表以备后用。获取图标列表命令：`python -c "from qfluentwidgets import FluentIcon as FIF; print('\n'.join(sorted([attr for attr in dir(FIF) if not attr.startswith('_')])))"`

### 第二阶段：核心功能模块开发 (预计5-7天)
- **里程碑**: 完成所有六个核心功能视图的界面布局和静态功能实现。
1.  **硬件信息视图 (`HardwareInfoView`)**:
    - **目标**: 清晰、优雅地展示电脑硬件的摘要与详细配置信息。
    - **实现**:
        - 视图整体包裹在 `SmoothScrollArea` 中。
        - **系统信息卡片**: 在顶部放置一个系统信息卡片，使用 `FIF.HOME` 图标，包含系统名称、版本、架构和主机名信息。
        - **详细信息区**: 在系统信息卡片下方，由多个 `CardWidget` 构成，每个卡片代表一个硬件类别，并配备相应的图标：
            - **CPU信息**: 使用 `FIF.LABEL` 图标
            - **主板信息**: 使用 `FIF.SETTING` 图标
            - **内存信息**: 使用 `FIF.LIBRARY` 图标
            - **GPU信息**: 使用 `FIF.PALETTE` 图标
            - **磁盘信息**: 使用 `FIF.FOLDER` 图标
2.  **优化清理视图 (`OptimizationView`)**:
    - **目标**: 提供多项系统和注册表清理功能。
    - **实现**:
        - 视图的主体是 `SegmentedWidget` 和 `QStackedWidget`，包含"电源选项"、"注册表优化"和"系统清理"三个页面。
        - **顶部操作区**: 提供一个全局的"执行选中任务" `PrimaryPushButton` 和一个全局的"全选/取消全选" `CheckBox`，以及一个居中显示的 `ProgressRing`。全选功能可以同时选择所有选项卡中的全部任务，实现真正的全局全选。
        - **电源选项卡**: 内部使用 `SmoothScrollArea` 包含一个 `CardWidget`，提供"解锁电源高级选项"的功能和相关复选框。
        - **注册表优化卡**: 内部使用 `SmoothScrollArea`，通过顶部全局的"全选/取消全选" `CheckBox` 控制所有复选框的状态。根据 `config/registry_commands.py` 中的分类，为每个分类动态创建一个 `CardWidget`，卡片内罗列该分类下的所有任务复选框。
        - **系统清理卡**: 实现方式同注册表优化卡，但数据源为 `config/system_cleanup.py`。
3.  **预装应用视图 (`AppRemovalView`)**:
    - **目标**: 提供卸载 Windows 预装应用和 OneDrive 的功能。
    - **实现**:
        - 视图的主体是 `SegmentedWidget` 和 `QStackedWidget`，包含"卸载 Windows 应用"和"卸载 OneDrive"两个页面。
        - **顶部操作区**: 提供一个全局的"卸载选中项" `PrimaryPushButton` 和一个全局的"全选/取消全选" `CheckBox`。全选功能可以同时选择所有选项卡中的全部应用，实现真正的全局全选。
        - **卸载 Windows 应用卡**: 内部使用 `SmoothScrollArea`，通过顶部全局的"全选/取消全选" `CheckBox` 控制所有复选框的状态。根据 `config/appx_packages.py` 中的分类，为每个分类动态创建一个 `CardWidget`，卡片内罗列该分类下的所有应用复选框。
        - **卸载 OneDrive 卡**: 内部使用 `SmoothScrollArea`，实现方式同卸载Windows应用卡，但数据源为 `config/onedrive_cleanup.py`。
4.  **超频工具视图 (`OverclockingView`)**:
    - **目标**: 提供启动超频工具和进入BIOS的功能。
    - **实现**:
        - 在视图顶部，使用一个独立的 `CardWidget` 来承载"一键进入 BIOS"的功能，包含说明和执行按钮。
        - 在下方设计并实现一个基于 `SmoothScrollArea` 的滚动布局，用于展示工具列表。
        - 动态扫描 `OCTools` 目录，为每个子文件夹创建一个固定高度的 `CardWidget` 作为列表项。
        - 每个工具卡片内部采用横向布局，包含：从 `.exe` 文件提取的**图标**、加粗的**工具名称**，以及一个"启动"按钮。
        - **图标提取逻辑实现**:
            1. 使用 `utils/icon_extractor.py` 中的工具类从 `.exe` 文件中提取图标。
            2. 使用 `icon.availableSizes()` 获取图标的所有可用尺寸，并实现算法选择最佳图标尺寸。
            3. 仅提取.exe文件的图标，忽略其他类型文件。
5.  **快捷工具视图 (`QuickToolsView`)**:
    - **目标**: 提供常用系统操作的快捷入口。
    - **实现**:
        - 视图整体包裹在 `SmoothScrollArea` 中。
        - 读取 `config/quick_tools.py`，根据定义的分类，创建多个 `CardWidget`，每个卡片代表一个工具分类。
        - 每个分类卡片包含一个带图标的标题头。
        - 在标题头下方，使用 `FlowLayout`（流式布局）来排列该分类下的所有工具按钮。
        - 工具按钮本身为"图标+文本"样式的 `CardWidget`，具有固定尺寸和悬浮效果。
6.  **关于软件视图 (`AboutView`)**:
    - **目标**: 以标准的 Fluent Design 风格展示软件、作者信息及提供相关链接。
    - **实现**:
        - **顶部**: 创建一个横向布局，包含应用图标、名称、版本号和版权信息。
        - **内容**: 使用 `SettingCardGroup` 创建两个分组：
            - **第一分组（操作）**: 包含"检查更新"和"赞助作者"两个 `PushSettingCard`。点击"赞助作者"后，弹出一个继承于 `MessageBoxBase` 对话框，内部使用 `SegmentedWidget` 选项卡来切换显示支付宝和微信的二维码。
            - **第二分组（链接）**: 包含指向作者主页、QQ群和项目源码的 `HyperlinkCard`。

### 第三阶段：后端逻辑与功能实现 (预计4-6天)
- **里程碑**: 将所有UI操作与后端的系统命令执行逻辑完全打通。
1.  **核心执行逻辑**:
    - 创建一个或多个核心模块 (`core`) 来处理 PowerShell、注册表、命令行等命令的执行。
    - 确保所有需要管理员权限的命令都能正确请求提权并执行。
    - 实现检查可执行文件是 GUI 还是 CLI 的逻辑。
2.  **功能逻辑实现**:
    - **优化清理**: 实现"一键执行"和单独执行任务的后台逻辑。执行完成后，实现重启资源管理器和删除图标缓存的后处理逻辑。
    - **预装应用**: 实现调用 PowerShell 脚本卸载应用（`AppxPackage` 和 `AppxProvisionedPackage`）的后台逻辑。
    - **超频工具**: 实现启动外部 `.exe` 程序（优先启动同名exe）和重启进入 BIOS 的功能。
    - **快捷工具**: 实现关机、重启、进入安全模式（一次性）等系统命令的调用。
3.  **交互完善**:
    - 为所有需要二次确认的操作添加 `MessageBox` 对话框。
    - 为赞助功能实现带有选项卡切换二维码的弹窗逻辑。
    - 实现长耗时任务的异步执行，避免UI卡死（例如，使用 `QThread`）。

### 第四阶段：测试、优化与文档完善 (预计2-3天)
- **里程碑**: 应用功能稳定，无明显BUG，文档齐全。
1.  **全面测试**:
    - 在不同的 Windows 环境下测试所有功能。
    - 检查UI布局在不同缩放下的表现。
    - 确保所有按钮、选项和执行逻辑均符合预期。
2.  **代码优化**:
    - 重构冗余代码。
    - 增加必要的错误处理和日志记录。
    - 优化性能，特别是硬件信息获取和长任务执行部分。
3.  **文档完善**:
    - 创建 `README.md`，详细说明项目功能、如何使用和如何贡献。
    - 撰写 `logs.md` 记录开发过程中的重要变更。
    - 确保所有代码都有清晰、必要的注释。

## 3. 技术选型
- **GUI 框架**: PySide6
- **UI 组件库**: PySide6-Fluent-Widgets
- **硬件信息**: `wmi`, `py-cpuinfo`, `psutil` (待定)
- **Windows API 调用**: `ctypes`, `win32api` (待定)

## 4. 风险管理
- **兼容性问题**: 不同 Windows 版本（Win10, Win11）的 PowerShell 命令和注册表项可能存在差异。**对策**: 在开发中优先考虑通用命令，对特定版本的命令进行测试和标注。
- **权限问题**: 管理员权限的获取和UAC弹窗可能会影响用户体验。**对策**: 明确告知用户为何需要管理员权限，并尽可能减少需要提权的操作。
- **外部工具依赖**: `OCTools` 中的工具可能自身存在问题。**对策**: 本项目只负责调用，不负责工具本身的功能。 