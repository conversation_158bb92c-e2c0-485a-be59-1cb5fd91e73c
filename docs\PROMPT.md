
# 项目提示词：Windows 硬件工具箱

## 1. 项目核心目标
使用 `PySide6` 和 `PySide6-Fluent-Widgets` 库，开发一款功能全面、界面现代化的 Windows 硬件工具箱。

## 2. 界面设计要求
- **布局**: 左侧为导航栏，右侧为内容显示区域。
- **风格**: 遵循 Fluent Design 设计语言，界面要求简洁、优雅、直观、易用。
- **参考**: [微软 Fluent Design 设计体系](https://learn.microsoft.com/zh-cn/windows/apps/design/)
- **图标**:
    - 主窗口图标使用 `assets/icon.ico`。
    - 界面内部优先使用 `PySide6-Fluent-Widgets` 提供的 `FluentIcon` 图标。

## 3. 项目文件结构
- 需要规划合理、清晰的项目文件结构。

## 4. 功能模块详述

### 4.1 左侧导航栏
- **硬件信息**
- **优化清理**
- **预装应用**
- **超频工具**
- **快捷工具**
- **关于软件**

### 4.2 右侧内容视图
**通用要求**:
- 每个视图的顶部都必须包含一个明确的标题和对该功能的简要描述。
- 主要操作按钮（如"执行选中任务"、"卸载选中项"）统一放置在标题下方的顶部区域，方便用户快速访问。
- 对于提供多项选择的列表视图，在标题下方的操作区域提供一个全局的"全选/取消全选"复选框，以便一键选择当前页面的所有项目。

#### 4.2.1 硬件信息 (Hardware Info)
- **功能**: 清晰、优雅地展示电脑硬件的摘要与详细配置信息。
- **布局**:
    - 整体包裹在 `SmoothScrollArea` 中，确保内容可滚动。
    - **系统信息卡片**:
        - 在顶部放置一个系统信息卡片，使用 `FIF.HOME` 图标，包含系统名称、版本、架构和主机名信息。
    - **详细信息区**:
        - 位于系统信息卡片下方，由多个 `CardWidget` 构成，每个卡片代表一个硬件类别，并配备相应的图标：
            - **CPU信息**: 使用 `FIF.LABEL` 图标
            - **主板信息**: 使用 `FIF.SETTING` 图标
            - **内存信息**: 使用 `FIF.LIBRARY` 图标
            - **GPU信息**: 使用 `FIF.PALETTE` 图标
            - **磁盘信息**: 使用 `FIF.FOLDER` 图标
- **特殊要求**: 无需动态刷新、分类、搜索、收藏等任何额外功能。

#### 4.2.2 优化清理 (Optimization & Cleanup)
- **数据源**:
    - **注册表优化**: `config/registry_commands.py`
    - **系统清理**: `config/system_cleanup.py` (待创建)
- **布局**:
    - 视图内容由一个 `SegmentedWidget` 和一个 `QStackedWidget` 构成，用于在"电源选项"、"注册表优化"和"系统清理"三个页面间切换。
    - 视图顶部提供一个"执行选中任务"的 `PrimaryPushButton` 和一个全局"全选/取消全选"的 `CheckBox`。
- **交互**:
    - 用户可以通过勾选各项任务，然后点击顶部的"执行选中任务"按钮来批量执行。
    - 全选复选框可以一键选择或取消选择所有选项卡中的全部任务，实现真正的全局全选功能。
- **后处理**: 所有任务执行完毕后，需要自动重启文件资源管理器 (`explorer.exe`) 并删除图标缓存数据库 (`iconcache.db`)。
- **执行策略**: 在执行任何 PowerShell 脚本前，需先将执行策略设置为 `Bypass`。
- **核心功能**:
    1.  **电源选项卡**:
        - 页面内使用 `SmoothScrollArea` 包含一个 `CardWidget`，用于展示"解锁电源高级选项"功能及相关复选框。
    2.  **注册表优化卡**:
        - **数据源**: `config/registry_commands.py`。
        - **布局**: 页面内使用 `SmoothScrollArea` 包含多个 `CardWidget`，每个卡片代表一个优化类别。卡片内以列表形式展示该类别下的所有可优化项（`CheckBox`）。
    3.  **系统清理卡**:
        - **数据源**: `config/system_cleanup.py`。
        - **布局**: 页面内使用 `SmoothScrollArea` 包含多个 `CardWidget`，每个卡片代表一个清理类别。卡片内以列表形式展示该类别下的所有可清理项（`CheckBox`）。
- **特殊要求**: 无需分类、搜索、收藏、刷新等任何额外功能。

#### 4.2.3 预装应用 (Pre-installed Apps)
- **数据源**:
    - **卸载 Windows 应用**: `config/appx_packages.py`
    - **卸载 OneDrive**: `config/onedrive_cleanup.py` (待创建)
- **布局**:
    - 视图内容由一个 `SegmentedWidget` 和一个 `QStackedWidget` 构成，用于在"卸载 Windows 应用"和"卸载并清理 OneDrive"两个页面间切换。
    - 视图顶部提供一个"卸载选中项"的 `PrimaryPushButton` 和一个全局"全选/取消全选"的 `CheckBox`。
- **交互**:
    - 用户可以通过勾选各项，然后点击顶部的"卸载选中项"按钮来批量执行。
    - 全选复选框可以一键选择或取消选择所有选项卡中的全部应用，实现真正的全局全选功能。
- **核心功能**:
    1.  **卸载 Windows 应用卡**:
        - **数据源**: `config/appx_packages.py`。
        - **布局**: 页面内使用 `SmoothScrollArea` 包含多个 `CardWidget`，每个卡片代表一个应用类别。卡片内以列表形式展示该类别下的所有可卸载应用（`CheckBox`）。
        - **执行逻辑**: 首先使用通配符匹配系统中的应用包名，然后同时卸载预装包 (`AppxPackage`) 和用户安装的包 (`AppxProvisionedPackage`)。
    2.  **卸载并清理 OneDrive 卡**:
        - **数据源**: `config/onedrive_cleanup.py`。
        - **布局**: 页面内使用 `SmoothScrollArea` 包含一个 `CardWidget`，用于展示所有清理步骤（`CheckBox`）和警告信息。
- **特殊要求**: 无需分类、搜索、收藏、刷新等任何额外功能。

#### 4.2.4 超频工具 (Overclocking Tools)
- **数据源**: `OCTools` 目录
- **布局**:
    - 视图顶部使用一个独立的 `CardWidget` 承载"一键进入 BIOS"功能，包含功能说明和执行按钮。
    - 下方是一个 `SmoothScrollArea`，用于展示所有超频工具。
- **功能**:
    - "一键进入 BIOS"功能在执行前需要用户二次确认。
    - 动态扫描 `OCTools` 目录下的所有子文件夹。每个子文件夹代表一个工具。
    - **工具命名**: 使用 `OCTools` 下的子文件夹名称作为工具的显示名称。
    - **工具列表**:
        - 以列表形式展示，每一行都是一个独立的、固定高度的 `CardWidget`。
        - 每个卡片内部包含：从 `.exe` 文件提取的**工具图标**、加粗的**工具名称**和一个"启动"按钮，整体采用横向布局。
        - 自动提取工具可执行文件（`.exe`）的图标作为工具图标。
    - **执行逻辑**:
        - 点击"启动"按钮时，优先执行与文件夹同名的 `.exe` 文件，若找不到，则执行该文件夹下的其他 `.exe` 文件。
        - 在启动前，需检测程序是 GUI 类型还是控制台（CLI）类型，并以合适的方式启动。
        - 所有可执行文件都必须以管理员权限运行。
- **特殊要求**: 无需分类、搜索、收藏、刷新等任何额外功能。

#### 4.2.5 快捷工具 (Quick Tools)
- **数据源**: `config/quick_tools.py`
- **布局**:
    - 视图整体包裹在 `SmoothScrollArea` 中。
    - 根据配置文件中的分类，创建多个 `CardWidget`，每个卡片代表一个工具分类。
    - **分类卡片**:
        - 每个卡片顶部有一个带 `FluentIcon` 图标和文本的标题。
        - 标题下方使用 `FlowLayout`（流式布局）排列该分类下的所有工具按钮。
    - **工具按钮**:
        - 每个按钮本身也是一个"图标+文本"样式的 `CardWidget`，具有固定的大小，点击时有悬浮效果。
- **二次确认**: 以下工具在执行前需要用户二次确认：
    - `安全模式`
    - `重新启动`
    - `关机`
    - `睡眠`
    - `锁定计算机`
- **安全模式特殊逻辑**: 进入安全模式的操作应是一次性的。即，在安全模式下重启后，系统应能恢复到正常启动模式。
- **特殊要求**: 无需搜索、收藏、刷新等任何额外功能。

#### 4.2.6 关于软件 (About)
- **布局**: 遵循标准的 Fluent Design 关于页面布局。
    - **顶部区域**: 一个横向布局，包含应用图标（`assets/icon.ico`）、应用名称、版本号和版权信息。
    - **内容区域**: 使用 `SettingCardGroup` 对功能和链接进行分组。
- **功能**:
    - **第一分组（操作）**:
        - **检查更新**: 使用 `PushSettingCard` 实现。
        - **赞助作者**: 使用 `PushSettingCard` 实现，点击后弹出一个继承于 `MessageBoxBase` 对话框。对话框内部使用 `SegmentedWidget` 选项卡来切换显示支付宝和微信的二维码。
    - **第二分组（链接）**:
        - **作者信息**: 使用 `HyperlinkCard` 链接到作者的 GitHub 主页。
        - **加入QQ群**: 使用 `HyperlinkCard` 提供加入QQ群的链接。
        - **项目源码**: 使用 `HyperlinkCard` 链接到项目的 GitHub 仓库。
- **特殊要求**: 无需搜索、收藏、刷新等任何额外功能。

## 5. 补充说明
- **严格遵守**: 必须严格按照本文档描述的需求进行开发，禁止添加任何未经确认的功能。界面内部优先使用 `PySide6-Fluent-Widgets` 提供的 `FluentIcon` 图标(`fluent_icons.txt`文件中)。