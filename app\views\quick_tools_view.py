#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QFrame, QApplication
from PySide6.QtCore import QEvent
from qfluentwidgets import (
    CardWidget,
    FlowLayout,
    SubtitleLabel,
    BodyLabel,
    IconWidget,
    FluentIcon as FIF,
    InfoBar,
    InfoBarPosition,
)
import os
import sys
import weakref
from typing import Dict, List, Any, Optional

from config.quick_tools import QUICK_TOOLS
from app.core.quick_tools_logic import QuickToolsLogic


class QuickToolsView(QWidget):
    """快捷工具视图"""

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("quickToolsView")

        # 创建业务逻辑处理器
        self.logic = QuickToolsLogic(self)

        # 连接信号
        self.logic.commandFinished.connect(self.onCommandFinished)
        self.logic.infoMessage.connect(self.showInfoMessage)

        # 创建顶层布局
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(16, 16, 16, 16)
        self.vBoxLayout.setSpacing(12)
        self.vBoxLayout.setAlignment(Qt.AlignTop)

        # 创建标题和描述
        self.titleLabel = SubtitleLabel("快捷工具", self)
        self.descriptionLabel = BodyLabel("常用系统工具集合，快速访问系统功能", self)
        self.vBoxLayout.addWidget(self.titleLabel)
        self.vBoxLayout.addWidget(self.descriptionLabel)
        self.vBoxLayout.addSpacing(12)

        # 保存创建的卡片引用，以防止被垃圾回收
        self.categoryCards = []

        # 保存所有创建的FlowLayout，以便在析构时主动清理引用
        # 使用弱引用列表，避免循环引用
        self.flowLayouts = []

        # 保存所有创建的按钮，以便在析构时主动清理引用
        self.toolButtons = []

        # 加载工具
        self.loadQuickTools()

        # 初始化界面
        self.initUI()

    def __del__(self):
        """析构函数，确保清理资源"""
        try:
            # 先断开所有信号
            if hasattr(self, "logic"):
                if hasattr(self.logic, "commandFinished"):
                    self.logic.commandFinished.disconnect(self.onCommandFinished)
                if hasattr(self.logic, "infoMessage"):
                    self.logic.infoMessage.disconnect(self.showInfoMessage)

            # 清理所有工具按钮
            for btn in self.toolButtons[:]:
                if btn:
                    # 断开信号
                    try:
                        btn.clicked.disconnect()
                    except:
                        pass
                    # 移除父对象
                    btn.setParent(None)

            # 清空按钮列表
            self.toolButtons.clear()

            # 清理所有卡片
            for card in self.categoryCards[:]:
                if card:
                    card.setParent(None)

            # 清空卡片列表
            self.categoryCards.clear()

            # 清空FlowLayout引用
            self.flowLayouts.clear()
        except:
            # 忽略清理过程中的错误
            pass

    def closeEvent(self, event):
        """窗口关闭事件处理

        确保在窗口关闭前清理所有资源

        Args:
            event: 关闭事件
        """
        # 先调用__del__进行资源清理
        self.__del__()
        # 然后调用父类的closeEvent
        super().closeEvent(event)

    def initUI(self):
        """初始化界面"""
        # 这个方法可以留空，因为我们已经在loadQuickTools中完成了布局
        pass

    def loadQuickTools(self):
        """加载快捷工具"""
        # 从配置中加载快捷工具
        try:
            # 按照分类创建卡片
            for category in QUICK_TOOLS:
                category_name = category.get("category", "未分类")
                category_icon = getattr(FIF, category.get("icon", "DOCUMENT"))
                tools = category.get("tools", [])

                # 创建分类卡片
                if tools:
                    card = self.createCategoryCard(category_name, category_icon, tools)
                    self.vBoxLayout.addWidget(card)

                    # 保存引用，防止被垃圾回收
                    self.categoryCards.append(card)

        except Exception as e:
            print(f"加载快捷工具失败: {e}")

    def createCategoryCard(self, category_name, category_icon, tools):
        """创建分类卡片"""
        card = CardWidget(self)
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(16, 16, 16, 16)
        card_layout.setSpacing(8)

        # 创建标题区域
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(8)

        icon_widget = IconWidget(category_icon, card)
        icon_widget.setFixedSize(18, 18)
        title_layout.addWidget(icon_widget)

        title_label = SubtitleLabel(category_name, card)
        title_layout.addWidget(title_label)
        title_layout.addStretch(1)

        card_layout.addLayout(title_layout)
        card_layout.addSpacing(4)

        # 创建流式布局，存放工具按钮
        flow_layout = FlowLayout()
        flow_layout.setContentsMargins(0, 0, 0, 0)
        flow_layout.setHorizontalSpacing(12)
        flow_layout.setVerticalSpacing(12)

        # 保存FlowLayout引用
        self.flowLayouts.append(flow_layout)

        # 添加工具按钮
        for tool_info in tools:  # 修改这里：直接遍历工具列表
            tool_name = tool_info.get("name", "")
            if not tool_name:
                continue

            button = self.createToolButton(tool_name, tool_info, card)
            flow_layout.addWidget(button)
            # 保存按钮引用
            self.toolButtons.append(button)

        card_layout.addLayout(flow_layout)

        return card

    def createToolButton(self, tool_name, tool_info, parent=None):
        """创建工具按钮"""
        # 创建卡片式按钮
        button = CardWidget(parent)
        button.setObjectName(f"ToolButton_{tool_name}")
        button.setFixedSize(160, 40)

        # 按钮内部布局
        layout = QHBoxLayout(button)
        layout.setContentsMargins(10, 0, 10, 0)
        layout.setSpacing(8)

        # 获取图标
        icon_name = tool_info.get("icon")
        icon = getattr(FIF, icon_name)

        # 添加图标
        iconWidget = IconWidget(icon, button)
        iconWidget.setFixedSize(18, 18)
        layout.addWidget(iconWidget)

        # 文本标签
        label = BodyLabel(tool_name, button)
        label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        layout.addWidget(label)

        # 如果需要确认或者需要管理员权限，添加信息图标指示
        require_confirm = tool_info.get("require_confirm", False)
        require_admin = tool_info.get("require_admin", False)

        if require_confirm or require_admin:
            infoIcon = IconWidget(
                FIF.INFO if require_confirm else FIF.CERTIFICATE, button
            )
            infoIcon.setFixedSize(14, 14)
            layout.addWidget(infoIcon)

        # 存储工具数据
        button.setProperty("tool_data", tool_info)
        button.setProperty("tool_name", tool_name)

        # 使用函数闭包捕获正确的工具名称和信息
        def create_click_handler(tool_name, tool_info):
            def handler():
                self.onToolButtonClicked(tool_name, tool_info)

            return handler

        # 绑定点击事件处理器
        handler = create_click_handler(tool_name, tool_info)
        button.clicked.connect(handler)

        # 将处理器保存为按钮的属性，防止被垃圾回收
        button._click_handler = handler

        return button

    def onToolButtonClicked(self, name, info):
        """处理工具按钮点击事件"""
        command = info.get("command", "")
        # 从配置获取或根据工具名称判断是否需要确认和管理员权限
        require_confirm = (
            info.get("require_confirm", False) or name in self.logic.CONFIRM_TOOLS
        )
        require_admin = (
            info.get("require_admin", False) or name in self.logic.ADMIN_TOOLS
        )

        # 检查命令是否为空
        if not command:
            InfoBar.error(
                title="命令错误",
                content=f"{name} 没有指定有效的命令",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self,
            )
            return

        # 需要安全模式恢复
        if name == "安全模式":
            # 确认后执行
            if not require_confirm:
                self.executeToolWithRecovery(name, command, require_admin)
                return

            # 二次确认对话框
            from app.core.dialog_manager import get_dialog_manager

            dialog_manager = get_dialog_manager()
            dialog_manager.show_confirmation_dialog(
                title="确认操作",
                content=f"即将执行: {name}\n此操作将重启计算机并进入安全模式，确定继续吗？",
                on_confirm=lambda: self.executeToolWithRecovery(
                    name, command, require_admin
                ),
                parent=self,
            )
            return

        # 需要确认的工具
        if require_confirm:
            from app.core.dialog_manager import get_dialog_manager

            dialog_manager = get_dialog_manager()
            dialog_manager.show_confirmation_dialog(
                title="确认操作",
                content=f"即将执行: {name}\n确定继续吗？",
                on_confirm=lambda: self.executeTool(name, command, require_admin),
                parent=self,
            )
        else:
            # 直接执行
            self.executeTool(name, command, require_admin)

    def executeTool(self, name, command, require_admin=False):
        """执行工具"""
        # 委托给逻辑处理器执行
        self.logic.execute_tool(name, command, require_admin)

    def executeToolWithRecovery(self, name, command, require_admin=False):
        """执行带恢复的工具（主要用于安全模式）"""
        # 委托给逻辑处理器执行
        self.logic.execute_tool_with_recovery(name, command, require_admin)

    def onCommandFinished(self, name, success):
        """命令执行完成回调"""
        # 显示执行结果
        if success:
            InfoBar.success(
                title="执行成功",
                content=f"{name} 已成功执行",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self,
            )
        else:
            InfoBar.error(
                title="执行失败",
                content=f"{name} 执行失败",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self,
            )

    def showInfoMessage(self, title, message):
        """显示信息消息"""
        InfoBar.info(
            title=title,
            content=message,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self,
        )
