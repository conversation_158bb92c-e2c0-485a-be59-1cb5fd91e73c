#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
命令执行引擎
提供用于执行Windows命令、PowerShell脚本和注册表操作的功能

此模块是应用程序的核心组件之一，负责安全、可控地执行各类系统命令，
包括普通CMD命令、PowerShell脚本、注册表操作和可执行文件调用。
支持管理员权限提升、同步/异步执行、进度反馈和错误处理。
"""

import os
import subprocess
import sys
import threading
import traceback
from enum import Enum
from typing import List, Dict, Any, Callable, Optional, Union, Tuple

import win32api
import win32con
import win32gui
import win32process
import ctypes
from PySide6.QtCore import QObject, Signal, QThread


class CommandError(Exception):
    """命令执行错误基类

    所有命令执行相关异常的基类，提供基本的错误信息处理
    """

    pass


class AdminPrivilegeError(CommandError):
    """管理员权限错误

    当需要管理员权限执行命令，但当前进程没有管理员权限时抛出此异常
    """

    def __init__(self, message="需要管理员权限，但当前不是管理员模式"):
        super().__init__(message)


class CommandExecutionError(CommandError):
    """命令执行错误

    当命令执行过程中出现错误时抛出此异常，包含命令本身、错误信息和退出码
    """

    def __init__(self, command: str, error: str, exit_code: int = -1):
        self.command = command
        self.error = error
        self.exit_code = exit_code
        super().__init__(f"执行命令失败 (退出码: {exit_code}): {error}")


class CommandType(Enum):
    """命令类型枚举

    定义支持的不同命令类型，用于确定命令的执行方式和环境
    """

    CMD = "cmd"  # CMD命令，通过cmd.exe /c执行
    POWERSHELL = "ps"  # PowerShell命令，通过powershell.exe执行
    REGISTRY = "reg"  # 注册表命令，通过cmd.exe /c执行
    EXECUTABLE = "exe"  # 可执行文件，直接执行


class CommandResult:
    """命令执行结果

    封装命令执行后的结果信息，包括成功状态、输出、错误和退出码
    """

    def __init__(
        self, success: bool, output: str = "", error: str = "", exit_code: int = 0
    ):
        """初始化命令执行结果

        Args:
            success: 命令是否成功执行
            output: 命令的标准输出内容
            error: 命令的标准错误输出
            exit_code: 命令的退出码，0通常表示成功
        """
        self.success = success
        self.output = output
        self.error = error
        self.exit_code = exit_code

    def __str__(self):
        """返回结果的字符串表示

        根据命令成功与否返回不同格式的信息

        Returns:
            str: 结果的描述字符串
        """
        if self.success:
            return f"CommandResult(success=True, exit_code={self.exit_code})"
        else:
            return f"CommandResult(success=False, exit_code={self.exit_code}, error={self.error})"


class CommandRunner(QObject):
    """命令执行器

    提供执行各种系统命令的核心功能，支持同步和异步执行，
    支持管理员权限提升，提供命令执行状态的信号通知
    """

    # 信号定义
    commandStarted = Signal(str)  # 命令开始执行，参数为命令内容
    commandFinished = Signal(CommandResult)  # 命令执行完成，参数为执行结果
    commandProgress = Signal(str)  # 命令执行进度，参数为进度信息
    commandError = Signal(str)  # 命令执行错误，参数为错误信息

    def __init__(self, parent=None):
        """初始化命令执行器

        检测当前进程是否具有管理员权限

        Args:
            parent: 父QObject对象，默认为None
        """
        super().__init__(parent)
        self._admin_mode = self._is_admin()

    def _is_admin(self) -> bool:
        """检查当前程序是否以管理员权限运行

        使用Windows API检测当前进程的权限

        Returns:
            bool: 如果是管理员权限则返回True，否则返回False

        Raises:
            异常会被捕获并记录，返回False
        """
        try:
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception as e:
            print(f"检查管理员权限时出错: {e}")
            return False

    def _run_as_admin(self, command: str, params: str = "") -> bool:
        """以管理员权限运行命令

        使用ShellExecute API以提升的权限运行指定的命令

        Args:
            command: 要执行的命令或程序路径
            params: 命令参数，默认为空字符串

        Returns:
            bool: 是否成功启动

        Raises:
            OSError: 提权失败时抛出，包含详细的错误信息
        """
        try:
            result = ctypes.windll.shell32.ShellExecuteW(
                None, "runas", command, params, None, 1
            )
            if result <= 32:
                error_messages = {
                    0: "内存不足",
                    2: "文件未找到",
                    3: "路径未找到",
                    5: "拒绝访问",
                    8: "内存不足",
                    11: "BAD_FORMAT",
                    26: "共享冲突",
                    27: "文件关联不完整",
                    28: "DDE操作超时",
                    29: "DDE操作失败",
                    30: "DDE操作忙",
                    31: "没有关联的应用程序",
                    32: "没有DDE应用程序正在运行",
                }
                error_msg = error_messages.get(result, f"未知错误码 {result}")
                raise OSError(f"提升权限失败: {error_msg}")
            return result > 32
        except Exception as e:
            self.commandError.emit(f"提升权限失败: {str(e)}")
            raise

    def execute_command(
        self,
        command: str,
        command_type: CommandType = CommandType.CMD,
        admin: bool = False,
        async_mode: bool = False,
        callback: Optional[Callable[[CommandResult], None]] = None,
    ) -> Union[CommandResult, None]:
        """执行命令

        根据指定的命令类型和参数执行系统命令

        Args:
            command: 要执行的命令
            command_type: 命令类型，默认为CMD
            admin: 是否需要管理员权限，默认为False
            async_mode: 是否异步执行，默认为False
            callback: 异步执行完成后的回调函数，仅在async_mode=True时有效

        Returns:
            CommandResult或None: 同步模式返回CommandResult，异步模式返回None

        Raises:
            AdminPrivilegeError: 需要管理员权限但当前不是管理员模式
            ValueError: 命令为空或命令类型无效
            CommandExecutionError: 命令执行失败时可能抛出
        """
        if not command or not command.strip():
            raise ValueError("命令不能为空")

        # 检查是否需要提升权限
        if admin and not self._admin_mode:
            # 对于直接执行的可执行文件，使用ShellExecute提权
            if command_type == CommandType.EXECUTABLE:
                try:
                    success = self._run_as_admin(command, "")
                    return CommandResult(success=success)
                except Exception as e:
                    error_msg = f"以管理员权限执行可执行文件失败: {str(e)}"
                    self.commandError.emit(error_msg)
                    raise AdminPrivilegeError(error_msg)

            # 对于其他命令，需要重新启动当前脚本并传递命令
            error_msg = "需要管理员权限，但当前不是管理员模式"
            self.commandError.emit(error_msg)
            raise AdminPrivilegeError(error_msg)

        # 根据命令类型构建实际执行的命令
        try:
            actual_command, shell = self._build_command(command, command_type)
        except ValueError as e:
            self.commandError.emit(str(e))
            raise

        # 发出命令开始信号
        self.commandStarted.emit(command)

        # 异步执行
        if async_mode:
            self._execute_async(actual_command, shell, callback)
            return None

        # 同步执行
        return self._execute_sync(actual_command, shell)

    def _build_command(
        self, command: str, command_type: CommandType
    ) -> Tuple[List[str], bool]:
        """根据命令类型构建实际执行的命令

        将用户输入的命令转换为subprocess可执行的格式

        Args:
            command: 原始命令
            command_type: 命令类型

        Returns:
            Tuple[List[str], bool]: 实际命令和是否使用shell

        Raises:
            ValueError: 命令类型无效时抛出
        """
        if command_type == CommandType.CMD:
            return ["cmd.exe", "/c", command], False
        elif command_type == CommandType.POWERSHELL:
            return [
                "powershell.exe",
                "-NoProfile",
                "-ExecutionPolicy",
                "Bypass",
                "-Command",
                command,
            ], False
        elif command_type == CommandType.REGISTRY:
            return ["cmd.exe", "/c", command], False
        elif command_type == CommandType.EXECUTABLE:
            # 直接执行可执行文件，不使用shell
            return [command], False
        else:
            raise ValueError(f"无效的命令类型: {command_type}")

    def _execute_sync(self, command: List[str], shell: bool) -> CommandResult:
        """同步执行命令

        在当前线程中执行命令并等待结果

        Args:
            command: 要执行的命令
            shell: 是否使用shell

        Returns:
            CommandResult: 命令执行结果

        Raises:
            CommandExecutionError: 命令执行失败时可能在内部处理，不会直接抛出
        """
        cmd_str = " ".join(command)
        try:
            # 使用subprocess执行命令
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=shell,
                text=True,
                encoding="utf-8",
                errors="replace",
            )

            # 获取输出和错误
            stdout, stderr = process.communicate()

            # 检查执行结果
            success = process.returncode == 0
            result = CommandResult(
                success=success,
                output=stdout,
                error=stderr,
                exit_code=process.returncode,
            )

            # 发出命令完成信号
            self.commandFinished.emit(result)

            if not success:
                # 记录错误日志，但不抛出异常，保持与现有代码兼容
                print(f"命令执行返回非零退出码 ({process.returncode}): {stderr}")

            return result

        except Exception as e:
            # 处理异常
            error_msg = f"命令执行异常: {str(e)}"
            traceback_str = traceback.format_exc()
            print(f"{error_msg}\n{traceback_str}")

            self.commandError.emit(error_msg)
            result = CommandResult(success=False, error=error_msg)
            self.commandFinished.emit(result)
            return result

    def _execute_async(
        self,
        command: List[str],
        shell: bool,
        callback: Optional[Callable[[CommandResult], None]] = None,
    ) -> None:
        """异步执行命令

        在单独的线程中执行命令

        Args:
            command: 要执行的命令
            shell: 是否使用shell
            callback: 执行完成后的回调函数
        """
        # 创建执行线程
        thread = threading.Thread(
            target=self._thread_worker, args=(command, shell, callback)
        )
        thread.daemon = True
        thread.start()

    def _thread_worker(
        self,
        command: List[str],
        shell: bool,
        callback: Optional[Callable[[CommandResult], None]],
    ) -> None:
        """线程工作函数

        Args:
            command: 要执行的命令
            shell: 是否使用shell
            callback: 执行完成后的回调函数
        """
        try:
            result = self._execute_sync(command, shell)

            if callback:
                callback(result)
        except Exception as e:
            error_msg = f"线程执行命令异常: {str(e)}"
            traceback_str = traceback.format_exc()
            print(f"{error_msg}\n{traceback_str}")

            self.commandError.emit(error_msg)
            result = CommandResult(success=False, error=error_msg)
            self.commandFinished.emit(result)

            if callback:
                callback(result)


class CommandWorker(QThread):
    """命令执行工作线程

    使用QThread封装命令执行，提供更好的Qt集成
    """

    # 信号定义
    commandStarted = Signal(str)  # 命令开始执行
    commandFinished = Signal(CommandResult)  # 命令执行完成
    commandProgress = Signal(str)  # 命令执行进度
    commandError = Signal(str)  # 命令执行错误

    def __init__(
        self,
        command: str,
        command_type: CommandType = CommandType.CMD,
        admin: bool = False,
        parent=None,
    ):
        """初始化命令执行工作线程

        Args:
            command: 要执行的命令
            command_type: 命令类型，默认为CMD
            admin: 是否需要管理员权限，默认为False
            parent: 父QObject对象，默认为None
        """
        super().__init__(parent)
        self.command = command
        self.command_type = command_type
        self.admin = admin
        self.runner = CommandRunner()
        self.result = None
        self.error = None

        # 转发信号
        self.runner.commandStarted.connect(self.commandStarted)
        self.runner.commandFinished.connect(self._on_command_finished)
        self.runner.commandProgress.connect(self.commandProgress)
        self.runner.commandError.connect(self._on_command_error)

    def run(self):
        """执行命令（由QThread调用）

        在单独的线程中执行命令
        """
        try:
            self.result = self.runner.execute_command(
                command=self.command,
                command_type=self.command_type,
                admin=self.admin,
                async_mode=False,  # 在线程中同步执行
            )
        except Exception as e:
            self.error = str(e)
            traceback_str = traceback.format_exc()
            print(f"CommandWorker执行失败: {str(e)}\n{traceback_str}")
            self.commandError.emit(f"命令执行失败: {str(e)}")

    def _on_command_finished(self, result: CommandResult):
        """命令完成的回调

        Args:
            result: 命令执行结果
        """
        self.result = result
        self.commandFinished.emit(result)

    def _on_command_error(self, error: str):
        """命令错误的回调

        Args:
            error: 错误信息
        """
        self.error = error
        self.commandError.emit(error)


def is_gui_app(executable_path: str) -> bool:
    """检测可执行文件是GUI应用还是控制台应用

    通过解析PE头部判断应用程序的子系统类型

    Args:
        executable_path: 可执行文件路径

    Returns:
        bool: 如果是GUI应用返回True，否则返回False
    """
    if not os.path.exists(executable_path):
        print(f"文件不存在: {executable_path}")
        return False

    try:
        # 打开文件获取PE头信息
        with open(executable_path, "rb") as f:
            # 读取DOS头
            dos_header = f.read(64)
            if dos_header[0:2] != b"MZ":
                return False

            # 获取PE头偏移量
            pe_offset = int.from_bytes(dos_header[60:64], byteorder="little")
            f.seek(pe_offset)

            # 检查PE签名
            if f.read(4) != b"PE\0\0":
                return False

            # 跳过COFF头
            f.seek(pe_offset + 4 + 20)

            # 读取可选头部的魔数
            optional_magic = int.from_bytes(f.read(2), byteorder="little")
            if optional_magic not in (0x10B, 0x20B):  # PE32 or PE32+
                return False

            # 获取子系统值
            if optional_magic == 0x10B:  # PE32
                f.seek(pe_offset + 4 + 20 + 64)
            else:  # PE32+
                f.seek(pe_offset + 4 + 20 + 80)

            subsystem = int.from_bytes(f.read(2), byteorder="little")

            # 子系统为2表示控制台，为1或3表示GUI
            return subsystem in (1, 3)

    except Exception as e:
        print(f"检测应用类型时出错: {str(e)}")
        # 默认作为控制台应用处理
        return False


# 单例模式
_runner_instance = None


def get_command_runner() -> CommandRunner:
    """获取CommandRunner实例(单例模式)

    确保整个应用程序中只有一个CommandRunner实例

    Returns:
        CommandRunner: 命令执行器实例
    """
    global _runner_instance
    if _runner_instance is None:
        _runner_instance = CommandRunner()
    return _runner_instance
