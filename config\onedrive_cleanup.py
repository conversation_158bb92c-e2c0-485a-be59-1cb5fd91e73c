#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OneDrive 清理配置
"""

# OneDrive 清理任务列表
ONEDRIVE_CLEANUP_TASKS = [
    {
        "name": "停止 OneDrive 进程",
        "description": "终止所有 OneDrive 相关进程",
        "command": 'Get-Process -Name "OneDrive", "FileCoAuth" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue',
    },
    {
        "name": "卸载 OneDrive",
        "description": "通过内置脚本卸载 OneDrive",
        "commands": [
            # 32位系统
            'if (Test-Path "$env:SystemRoot\\System32\\OneDriveSetup.exe") { Start-Process "$env:SystemRoot\\System32\\OneDriveSetup.exe" -ArgumentList "/uninstall" -Wait }',
            # 64位系统
            'if (Test-Path "$env:SystemRoot\\SysWOW64\\OneDriveSetup.exe") { Start-Process "$env:SystemRoot\\SysWOW64\\OneDriveSetup.exe" -ArgumentList "/uninstall" -Wait }',
        ],
    },
    {
        "name": "删除 OneDrive 启动项",
        "description": "从注册表中删除 OneDrive 启动项",
        "commands": [
            'Remove-ItemProperty -Path "HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" -Name "OneDrive" -ErrorAction SilentlyContinue'
        ],
    },
    {
        "name": "删除 OneDrive 文件夹",
        "description": "删除 OneDrive 程序文件夹",
        "commands": [
            'Remove-Item -Path "$env:USERPROFILE\\OneDrive" -Recurse -Force -ErrorAction SilentlyContinue',
            'Remove-Item -Path "$env:LOCALAPPDATA\\Microsoft\\OneDrive" -Recurse -Force -ErrorAction SilentlyContinue',
            'Remove-Item -Path "$env:ProgramData\\Microsoft\\OneDrive" -Recurse -Force -ErrorAction SilentlyContinue',
        ],
    },
    {
        "name": "删除 OneDrive 右键菜单",
        "description": "从文件资源管理器中删除 OneDrive 右键菜单",
        "commands": [
            'Remove-Item -Path "HKCR:\\CLSID\\{018D5C66-4533-4307-9B53-224DE2ED1FE6}" -Recurse -Force -ErrorAction SilentlyContinue',
            'Remove-Item -Path "HKCR:\\Wow6432Node\\CLSID\\{018D5C66-4533-4307-9B53-224DE2ED1FE6}" -Recurse -Force -ErrorAction SilentlyContinue',
        ],
    },
    {
        "name": "删除 OneDrive 桌面图标",
        "description": "删除桌面上的 OneDrive 图标",
        "command": 'Remove-Item -Path "$env:USERPROFILE\\Desktop\\OneDrive.lnk" -Force -ErrorAction SilentlyContinue',
    },
    {
        "name": "禁用 OneDrive 自动安装",
        "description": "阻止 Windows 自动重新安装 OneDrive",
        "commands": [
            'if (!(Test-Path "HKLM:\\Software\\Policies\\Microsoft\\Windows\\OneDrive")) { New-Item -Path "HKLM:\\Software\\Policies\\Microsoft\\Windows\\OneDrive" -Force | Out-Null }',
            'Set-ItemProperty -Path "HKLM:\\Software\\Policies\\Microsoft\\Windows\\OneDrive" -Name "DisableFileSyncNGSC" -Type DWord -Value 1',
        ],
    },
]
