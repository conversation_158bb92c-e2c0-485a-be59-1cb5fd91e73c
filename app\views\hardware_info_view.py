#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel
from PySide6.QtGui import QColor
from qfluentwidgets import (
    SmoothScrollArea,
    SubtitleLabel,
    BodyLabel,
    CardWidget,
    IconWidget,
    FluentIcon as FIF,
    HorizontalSeparator,
)
import platform
import time

from app.core import get_hardware_manager


class HardwareInfoView(QWidget):
    """硬件信息视图"""

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("hardwareInfoView")

        # 创建顶层布局
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(16, 16, 16, 16)
        self.vBoxLayout.setSpacing(12)

        # 创建标题和描述
        self.titleLabel = SubtitleLabel("硬件信息", self)
        self.descriptionLabel = BodyLabel(
            "清晰、优雅地展示电脑硬件的摘要与详细配置信息", self
        )
        self.vBoxLayout.addWidget(self.titleLabel)
        self.vBoxLayout.addWidget(self.descriptionLabel)
        self.vBoxLayout.addSpacing(12)

        # 创建系统信息卡片
        self.systemCard = self.createInfoCard(FIF.HOME, "系统信息", "获取中...")
        self.vBoxLayout.addWidget(self.systemCard)
        self.vBoxLayout.addSpacing(16)

        # 创建滚动区域
        self.scrollArea = SmoothScrollArea(self)
        self.scrollWidget = QWidget(self)

        # 设置滚动区域
        self.scrollArea.setWidget(self.scrollWidget)
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setStyleSheet("background: transparent;")

        # 内容布局
        self.contentLayout = QVBoxLayout(self.scrollWidget)
        self.contentLayout.setContentsMargins(0, 0, 0, 0)
        self.contentLayout.setSpacing(16)
        self.contentLayout.setAlignment(Qt.AlignTop)

        # 将滚动区域添加到主布局
        self.vBoxLayout.addWidget(self.scrollArea)

        # 获取硬件管理器实例
        self.hardwareManager = get_hardware_manager()

        # 连接信号
        self.hardwareManager.hardwareInfoReady.connect(self.onHardwareInfoReady)
        self.hardwareManager.hardwareInfoError.connect(self.onHardwareInfoError)

        # 初始化界面
        self.initUI()

        # 加载硬件信息
        self.loadHardwareInfo()

    def createInfoCard(self, icon, title, content):
        """创建信息卡片"""
        card = CardWidget(self)
        cardLayout = QVBoxLayout(card)
        cardLayout.setContentsMargins(16, 16, 16, 16)
        cardLayout.setSpacing(10)

        # 创建图标和标题横向布局
        headerLayout = QHBoxLayout()
        headerLayout.setSpacing(8)

        # 添加图标
        iconWidget = IconWidget(icon, card)
        iconWidget.setFixedSize(20, 20)
        headerLayout.addWidget(iconWidget)

        # 添加标题
        titleLabel = BodyLabel(title, card)
        titleLabel.setObjectName("cardTitle")
        headerLayout.addWidget(titleLabel)
        headerLayout.addStretch(1)

        # 添加横向布局到卡片
        cardLayout.addLayout(headerLayout)

        # 添加分隔线
        separator = HorizontalSeparator(card)
        cardLayout.addWidget(separator)

        # 添加内容
        contentLabel = BodyLabel(content, card)
        cardLayout.addWidget(contentLabel)

        # 将内容标签存储为卡片属性，方便后续更新
        card.contentLabel = contentLabel

        return card

    def initUI(self):
        """初始化界面"""
        # CPU 信息区块
        self.cpuCard = self.createInfoCard(FIF.LABEL, "CPU 信息", "获取中...")
        self.contentLayout.addWidget(self.cpuCard)

        # 主板信息区块
        self.motherboardCard = self.createInfoCard(FIF.SETTING, "主板信息", "获取中...")
        self.contentLayout.addWidget(self.motherboardCard)

        # 内存信息区块
        self.memoryCard = self.createInfoCard(FIF.LIBRARY, "内存信息", "获取中...")
        self.contentLayout.addWidget(self.memoryCard)

        # GPU 信息区块
        self.gpuCard = self.createInfoCard(FIF.PALETTE, "GPU 信息", "获取中...")
        self.contentLayout.addWidget(self.gpuCard)

        # 磁盘信息区块
        self.diskCard = self.createInfoCard(FIF.FOLDER, "磁盘信息", "获取中...")
        self.contentLayout.addWidget(self.diskCard)

    def onHardwareInfoReady(self, hardware_info):
        """硬件信息准备完成的回调"""
        self.loadHardwareInfo(hardware_info)

    def onHardwareInfoError(self, error):
        """硬件信息获取失败的回调"""
        # 显示错误信息
        self.systemCard.contentLabel.setText(f"获取硬件信息失败: {error}")
        self.cpuCard.contentLabel.setText("获取失败")
        self.motherboardCard.contentLabel.setText("获取失败")
        self.memoryCard.contentLabel.setText("获取失败")
        self.gpuCard.contentLabel.setText("获取失败")
        self.diskCard.contentLabel.setText("获取失败")

    def loadHardwareInfo(self, hardware_info=None):
        """加载硬件信息

        Args:
            hardware_info: 硬件信息字典，包含格式化后的字符串信息
        """
        try:
            # 如果没有提供hardware_info，从硬件管理器获取或触发获取
            if hardware_info is None:
                # 尝试从硬件管理器获取缓存信息
                hardware_info = self.hardwareManager.get_hardware_info()

                # 如果硬件管理器中没有缓存信息，直接触发异步获取
                if not hardware_info:
                    self.hardwareManager.fetch_hardware_info()
                    return

            # 如果有格式化的信息，直接使用
            if "formatted" in hardware_info:
                formatted = hardware_info["formatted"]

                # 更新各个卡片的内容
                self.systemCard.contentLabel.setText(
                    formatted.get("system", "获取失败")
                )
                self.cpuCard.contentLabel.setText(formatted.get("cpu", "获取失败"))
                self.motherboardCard.contentLabel.setText(
                    formatted.get("motherboard", "获取失败")
                )
                self.memoryCard.contentLabel.setText(
                    formatted.get("memory", "获取失败")
                )
                self.gpuCard.contentLabel.setText(formatted.get("gpu", "获取失败"))
                self.diskCard.contentLabel.setText(formatted.get("disk", "获取失败"))
            else:
                # 如果是旧版本的数据格式，重新请求数据
                self.hardwareManager.fetch_hardware_info()

        except Exception as e:
            print(f"加载硬件信息失败: {e}")
            import traceback

            traceback.print_exc()

            # 显示错误信息
            self.onHardwareInfoError(str(e))
