#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PySide6.QtCore import Qt
from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QFrame,
)
from PySide6.QtGui import QPixmap
from qfluentwidgets import (
    SettingCardGroup,
    HyperlinkCard,
    PushSettingCard,
    FluentIcon as FIF,
    SubtitleLabel,
    BodyLabel,
    IconWidget,
    InfoBar,
    InfoBarPosition,
)
import os

from app.components.dialogs.donate_dialog import DonateDialog
from config import app_config


class AboutView(QWidget):
    """关于软件视图

    显示应用程序的基本信息、版本、作者信息和相关链接，
    并提供检查更新和赞助作者的功能。

    Attributes:
        titleLabel: 视图标题标签
        descriptionLabel: 描述文本标签
        appIconLabel: 应用程序图标标签
        appNameLabel: 应用程序名称标签
        versionLabel: 版本信息标签
        copyrightLabel: 版权信息标签
        updateCard: 检查更新卡片
        sponsorCard: 赞助作者卡片
        authorCard: 作者信息链接卡片
        qqGroupCard: QQ群链接卡片
        sourceCodeCard: 源码链接卡片
    """

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setObjectName("aboutView")

        # 创建顶层布局
        self.vBoxLayout = QVBoxLayout(self)
        self.vBoxLayout.setContentsMargins(16, 16, 16, 16)
        self.vBoxLayout.setSpacing(12)
        self.vBoxLayout.setAlignment(Qt.AlignTop)

        # 创建标题和描述
        self.titleLabel = SubtitleLabel("关于软件", self)
        self.descriptionLabel = BodyLabel(
            "查看软件信息、作者和更新，获取帮助和支持", self
        )
        self.vBoxLayout.addWidget(self.titleLabel)
        self.vBoxLayout.addWidget(self.descriptionLabel)
        self.vBoxLayout.addSpacing(12)

        # 创建顶部区域（图标、应用名称、版本等）
        self.topLayout = QHBoxLayout()
        self.topLayout.setContentsMargins(0, 0, 0, 0)
        self.topLayout.setSpacing(16)

        # 创建应用图标
        self.appIconLabel = QLabel(self)
        icon_path = "assets/icon.ico"
        if os.path.exists(icon_path):
            pixmap = QPixmap(icon_path)
            if not pixmap.isNull():
                pixmap = pixmap.scaled(
                    64, 64, Qt.KeepAspectRatio, Qt.SmoothTransformation
                )
                self.appIconLabel.setPixmap(pixmap)
                self.appIconLabel.setFixedSize(64, 64)
        else:
            # 使用默认图标
            self.iconWidget = IconWidget(FIF.INFO, self)
            self.iconWidget.setFixedSize(64, 64)
            self.topLayout.addWidget(self.iconWidget)
        self.topLayout.addWidget(self.appIconLabel)

        # 创建应用信息布局（名称、版本、版权）
        self.appInfoLayout = QVBoxLayout()
        self.appInfoLayout.setContentsMargins(0, 0, 0, 0)
        self.appInfoLayout.setSpacing(4)

        # 应用名称
        self.appNameLabel = QLabel(app_config.APP_NAME, self)
        font = self.appNameLabel.font()
        font.setPointSize(16)
        font.setBold(True)
        self.appNameLabel.setFont(font)
        self.appInfoLayout.addWidget(self.appNameLabel)

        # 版本信息
        self.versionLabel = QLabel(f"版本: {app_config.APP_VERSION}", self)
        self.appInfoLayout.addWidget(self.versionLabel)

        # 版权信息
        self.copyrightLabel = QLabel(app_config.COPYRIGHT, self)
        self.appInfoLayout.addWidget(self.copyrightLabel)

        # 将应用信息布局添加到顶部布局
        self.topLayout.addLayout(self.appInfoLayout)
        self.topLayout.addStretch(1)

        # 将顶部布局添加到主布局
        self.vBoxLayout.addLayout(self.topLayout)
        self.vBoxLayout.addSpacing(16)

        # 创建设置卡片组 - 操作
        self.actionGroup = SettingCardGroup("操作", self)

        # 添加"检查更新"卡片
        self.updateCard = PushSettingCard(
            icon=FIF.UPDATE,
            title="检查更新",
            content="检查软件是否有可用的更新",
            text="检查",
        )
        self.actionGroup.addSettingCard(self.updateCard)

        # 添加"赞助作者"卡片
        self.sponsorCard = PushSettingCard(
            icon=FIF.HEART,
            title="赞助作者",
            content="如果您喜欢这个软件，可以考虑赞助作者",
            text="赞助",
        )
        self.actionGroup.addSettingCard(self.sponsorCard)

        # 将操作组添加到主布局
        self.vBoxLayout.addWidget(self.actionGroup)

        # 创建设置卡片组 - 链接
        self.linkGroup = SettingCardGroup("链接", self)

        # 添加作者信息链接卡片
        self.authorCard = HyperlinkCard(
            icon=FIF.PEOPLE,
            title="作者信息",
            content="了解更多关于作者的信息",
            url=app_config.AUTHOR_URL,
            text="访问主页",
        )
        self.linkGroup.addSettingCard(self.authorCard)

        # 添加QQ群链接卡片
        self.qqGroupCard = HyperlinkCard(
            icon=FIF.CHAT,
            title="加入QQ群",
            content="加入QQ群与其他用户交流",
            url=app_config.QQ_GROUP_URL,
            text="加入我们",
        )
        self.linkGroup.addSettingCard(self.qqGroupCard)

        # 添加项目源码链接卡片
        self.sourceCodeCard = HyperlinkCard(
            icon=FIF.CODE,
            title="项目源码",
            content="在GitHub上查看项目源代码",
            url=app_config.SOURCE_CODE_URL,
            text="GitHub",
        )
        self.linkGroup.addSettingCard(self.sourceCodeCard)

        # 将链接组添加到主布局
        self.vBoxLayout.addWidget(self.linkGroup)

        # 初始化界面
        self.initUI()

    def initUI(self):
        """初始化界面

        连接信号和槽
        """
        # 连接按钮信号
        self.updateCard.button.clicked.connect(self.onCheckUpdateClicked)
        self.sponsorCard.button.clicked.connect(self.onSponsorClicked)

    def onCheckUpdateClicked(self):
        """检查更新按钮点击事件

        显示检查更新的进度信息，然后显示更新结果
        """
        # 显示检查更新信息
        InfoBar.info(
            title="检查更新",
            content="正在检查更新，请稍候...",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=2000,
            parent=self,
        )

        # TODO: 实际的检查更新逻辑
        # 这里只是模拟显示结果
        InfoBar.success(
            title="检查完成",
            content="当前已是最新版本",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP_RIGHT,
            duration=3000,
            parent=self,
        )

    def onSponsorClicked(self):
        """赞助作者按钮点击事件

        检查二维码文件是否存在，然后显示赞助对话框
        """
        # 检查二维码文件是否存在
        alipay_path = "assets/alipay.png"
        wechat_path = "assets/wechat_pay.png"

        if not os.path.exists(alipay_path) or not os.path.exists(wechat_path):
            InfoBar.warning(
                title="文件缺失",
                content="二维码文件不存在，请确保 assets 目录中包含 alipay.png 和 wechat_pay.png 文件",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP_RIGHT,
                duration=3000,
                parent=self,
            )
            return

        # 创建并显示赞助对话框
        dialog = DonateDialog(self)
        dialog.exec()
