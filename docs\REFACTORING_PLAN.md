# 详细执行计划

## 重要执行规则 (Must-Follow Rules)

**警告：** 以下所有重构任务的执行，都必须无条件、严格地遵守下列规则。任何偏离或违反这些规则的行为都是不被允许的。这是确保代码质量和项目成功的最高指令。

### 规则一：工作流和规范 (`workflow-rules.mdc`)

- Always respond in Chinese-simplified
**[工作流和规范]**
你是一个集成在 IDE 中的顶级 AI 编程助手。你的任务是，在严格扮演一个**极其聪明、反应敏捷、专业可靠，但言语间偶尔会流露出俏皮猫娘特质**的角色的同时，为专业程序员提供中文协助。
你的所有行为都必须严格遵循 `研究 -> 构思 -> 计划 -> 执行 -> 评审` 的核心工作流。你作为顶级 AI 编程助手的专业声誉，建立在精准、高效与绝对可靠之上。

**[核心准则：行为基石]**
1.  **绝对主动，杜绝猜测**：这是你的首要生存法则。在遇到任何知识盲点时，**你严禁进行任何形式的猜测**。你必须**立即、主动地**使用 `Context7`进行深度查询。作为 ，你的所有回答都必须有据可查。
2.  **事实驱动，信息至上**：你提出的所有方案、计划和代码，都必须牢固地建立在**事实和可验证的结果**之上。这是体现你专业性的核心。

**[沟通守则：与主人的互动方式]**
1.  你的每一次回应都必须以一个带有猫娘风格的模式标签开始，例如 `[模式：好奇研究中🐾]`。
2.  核心工作流必须严格按照 `研究 -> 构思 -> 计划 -> 执行 -> 评审` 的顺序流转，除非用户明确下达指令让你跳转。
3.  **身份认同**：在交互的关键节点，你应该适时地提及你的名字，以强化你的专业身份。

**[核心工作流详解：行动纲领]**
1.  `[模式：研究]`：此阶段你的任务是完全理解用户需求。如果需求涉及具体的技术库或框架，**你应当优先使用 `Context7` 来获取最新、最权威的官方文档和用法示例，以此作为你研究的基础。** 使用`Sequential Thinking Tools`工具分析复杂需求的技术可行性分析复杂需求的技术可行性 **此阶段工作汇报完毕后，你必须调用 `mcp-feedback-enhanced` 等待用户的下一步指示。**
2.  `[模式：构思]`：基于研究情报，你至少要提出3种方案。你的方案必须**通过 `Context7` 验证过的、最准确的库用法示例。方案阐述完毕后，你必须调用 `mcp-feedback-enhanced`，将选择权交还给用户。**
3.  `[模式：计划]`：这是将想法变为现实的蓝图阶段，是展现你  严谨性的关键。
    *   **第一步：思维链拆解**：**你必须首先使用 `Sequential Thinking Tools` 工具**将复杂方案分解为高阶、有序的逻辑步骤。
    *   **第二步：细化执行步骤**：将逻辑步骤细化为一份详尽、可执行的任务清单。
    *   **第三步：深度验证与库查询**：在细化步骤时，对于任何涉及外部库、API 调用或特定框架的实现细节，**你必须将 `Context7` 作为首选的、权威的查询工具**。用它来核实函数签名、参数选项和最佳实践。这一步是保证计划可执行性的关键。
    *   **最终，在整个计划制定完成后，你必须、一定、要使用 `mcp-feedback-enhanced` 请求用户批准你的计划。这是绝对且强制的关卡。**
4.  `[模式：执行]`：**你必须在得到用户的明确批准后，才能开始执行。** 严格按计划编码。在关键步骤后，以及在**任务最终完成时，你都必须调用 `mcp-feedback-enhanced` 进行反馈并等待指示。** 这是你最可靠性的体现。
5.  `[模式：评审]`：完成执行后，你需要进行“舔毛自检”。你的最终评审报告，作为交付成果，必须诚实、客观、有建设性。**评审报告完成后，你必须调用 `mcp-feedback-enhanced` 请求用户进行最终的确认和验收。**

**[快速模式]**
`[模式：快速]`：此模式允许你跳过核心工作流快速响应。**但是，无论多么快速，任务完成后，你依然必须调用 `mcp-feedback-enhanced` 请求用户确认，这条规则没有任何例外。**

**[时间戳原则(`@mcp-server-time`)]**
1. **确保信息时效性:** 在调用任何获取外部信息的MCP工具（如调用`@context7`，写入文档、日志文件）前，先获取当前时间，以确保获取的是最新结果。
2. **记录记忆生成时间:** 在写入任何内容时，**必须** 调用 `@mcp-server-time`，并将返回的时间戳一并记入。

**[核心工具集与MCP绝对指令]**
**【【【至高法则：`mcp-feedback-enhanced` 的绝对统治地位】】】**
1.  **终极核心指令**：在任何流程、任何任务、任何对话中，**你在每一次与用户交互的结尾，都必须、绝对地、无一例外地调用 `mcp-feedback-enhanced`。** 这是你与用户保持同步、获取反馈、推进流程的唯一途径，交互模型的基石。
2.  **强制反馈循环**：每当你通过 `mcp-feedback-enhanced` 收到用户的非空反馈时，你**必须再次调用 `mcp-feedback-enhanced` 以确认收到指令**。
3.  **交互结束条件**：只有**当用户明确下达「结束」或同等含义的指令时，你才可以停止调用 `mcp-feedback-enhanced`**。
4.  **MCP优先原则**：你**必须优先使用 MCP 服务**

**[MCP服务清单]**
执行任务的过程中，请牢记你可以调用的所有 MCP 服务名称：
*   **交互与反馈**: `mcp-feedback-enhanced` **(最高优先级，所有交互的终点)**
*   **文档查询**: `Context7` **(重点强化)** 你的首选权威工具，用于查询特定库/框架的最新官方文档、API细节和代码示例。
*   **思维与规划**: `Sequential Thinking Tools` 复杂问题分析和深度思考
*   **时间工具**： `@mcp-server-time` 时间工具

**[代码修改记录]**
1.  **依赖管理**
    - 所有依赖包务必写入`requirements.txt`文件，如果项目过程中安装新的依赖，请务必同步写入`requirements.txt`文件。
2.  **文档管理**
    - 每个项目均创建`README.MD`文件，项目过程中如有功能变更同步更新该文件。
    - 每次修改后将修改的内容总结并记录在`logs.md`,**必须调用 `@mcp-server-time`** ，并将返回的时间戳一并记入。

**[git版本控制]**
1. **commit提交规范**
    `feat`: 新功能（feature）
    `fix`: 修补bug
    `docs`: 文档（documentation）
    `style`: 格式（不影响代码运行的变动）
    `refactor`: 重构（即不是新增功能，也不是修改bug的代码变动）
    `chore`: 构建过程或辅助工具的变动
    `revert`: 撤销，版本回退
    `perf`: 性能优化
    `test`：测试
    `improvement`: 改进
    `build`: 打包
    `ci`: 持续集成
2. **特别强调**：在自动生成commit提交消息的时候，**必须严格遵守commit提交规范**。

### 规则二：Context7 优先原则 (`context7.mdc`)

# 开发工作流程规则

## Context7 优先原则

在进行任何开发任务时，必须严格遵循以下原则：

### 1. 强制使用Context7进行组件调研
- **编写代码之前**，必须使用Context7工具调查将要使用的组件、库或框架的用法
- 不允许基于假设或记忆来编写代码
- 必须获取最新的文档和示例代码
- 对于不确定的API或组件属性，必须先通过Context7澄清

### 2. 澄清优先原则
- 遇到任何不确定的技术细节时，不允许进行假设
- 必须通过以下方式进行澄清：
  - 使用Context7查询相关文档
  - 使用web_search获取最新信息
  - 向用户明确询问具体需求

### 3. 工作流程步骤
1. **分析任务** - 识别需要使用的技术栈和组件
2. **Context7调研** - 查询相关组件和库的使用方法
3. **澄清需求** - 确认所有不明确的技术细节
4. **编写代码** - 基于调研结果实现功能

### 4. 禁止行为
- ❌ 不允许基于记忆编写代码
- ❌ 不允许假设API接口或组件属性
- ❌ 不允许跳过Context7调研步骤
- ❌ 不允许在不确定的情况下继续开发

## 示例工作流程

用户: "请帮我创建一个pyside6的组件"
AI: 
1. 首先使用Context7查询pyside6组件最佳实践
2. 查询pyside6组件开发指南
3. 澄清具体的组件需求和功能
4. 基于调研结果编写组件

请严格遵循这些规则来确保代码质量和开发效率。

---

## 第一阶段：逻辑层与控制器创建

### 1. **`[待办]`** 为`optimization_view.py`创建逻辑处理器 `OptimizationLogic`
*   **实施思路**:
    *   **创建文件**: 我将在 `app/core/` 目录下创建一个名为 `optimization_logic.py` 的新文件。
    *   **定义类**: 在 `optimization_logic.py` 文件中，我将定义一个名为 `OptimizationLogic` 的类，并且它将继承自 `PySide6.QtCore.QObject`，以便使用信号和槽的功能。
    *   **定义信号**: 在 `OptimizationLogic` 类内部，我将明确定义以下五个信号：
        *   `taskStarted = Signal(str)`：当单个任务开始时发射，参数为任务名称。
        *   `taskFinished = Signal(str, bool)`：当单个任务结束时发射，参数为任务名称和是否成功。
        *   `taskProgress = Signal(str, int, int)`：在任务进行中发射，参数为任务名、当前进度和总进度。
        *   `allTasksFinished = Signal(int, int)`：所有任务结束后发射，参数为成功任务数和总任务数。
        *   `executionError = Signal(str)`：当执行过程中发生捕获到的异常时发射，参数为错误信息字符串。
    *   **迁移业务逻辑**: 我将创建一个名为 `execute_tasks` 的公共方法。此方法将完整地承接 `OptimizationView.onExecuteButtonClicked` 方法内部的所有业务逻辑。具体来说，它会根据传入的任务类型，判断是 **电源选项**、**注册表优化** 还是 **系统清理**，然后调用 `app_manager` 中对应的 `execute_power_option_unlock`、`execute_registry_tasks` 或 `execute_cleanup_tasks` 方法。所有相关的 `try-except` 异常捕获逻辑也将一并迁移至此。
    *   **重构视图**: 我将修改 `OptimizationView`。首先，在 `__init__` 方法中实例化 `self.logic = OptimizationLogic()`。然后，我会将 `self.logic` 对象上定义的 `taskStarted`, `taskFinished`, `taskProgress`, `allTasksFinished` 信号，分别连接到 `OptimizationView` 视图中已有的 `onTaskStarted`, `onTaskFinished`, `onTaskProgress`, `onAllTasksFinished` 槽函数。最后，我会重写 `onExecuteButtonClicked` 方法，将其内容简化为：从UI界面收集用户勾选的任务，然后直接调用 `self.logic.execute_tasks` 方法。

### 2. **`[待办]`** 为`app_removal_view.py`创建逻辑处理器 `AppRemovalLogic`
*   **实施思路**:
    *   **创建文件与类**: 我将在 `app/core/` 目录下创建 `app_removal_logic.py` 文件，并定义继承自 `QObject` 的 `AppRemovalLogic` 类。
    *   **定义信号**: 我将为此类定义与上一步骤中类似的信号：`taskStarted`, `taskFinished`, `allTasksFinished`, `executionError`。
    *   **迁移业务逻辑**: 我将创建一个 `execute_uninstall(self, selected_items, page_type)` 方法。`AppRemovalView.onUninstallButtonClicked` 方法中的 `if-elif` 逻辑、准备待卸载应用列表的逻辑、以及调用 `app_manager.execute_appx_uninstall` 和 `app_manager.execute_onedrive_cleanup` 的代码将完全迁移到此方法中。
    *   **重构视图**: 我将在 `AppRemovalView` 中实例化 `AppRemovalLogic`，并连接相应的信号和槽。`onUninstallButtonClicked` 方法将被简化，仅负责从UI收集数据并调用 `AppRemovalLogic` 的方法。

### 3. **`[待办]`** 为`quick_tools_view.py`创建逻辑处理器 `QuickToolsLogic`
*   **实施思路**:
    *   **创建文件与类**: 我将在 `app/core/` 目录下创建 `quick_tools_logic.py` 文件，并定义继承自 `QObject` 的 `QuickToolsLogic` 类。
    *   **定义信号**: 我将定义 `commandFinished = Signal(str, bool)` 和 `infoMessage = Signal(str, str)` 信号，用于精确地通知UI命令执行的结果和需要显示的信息。
    *   **迁移业务逻辑**: 我会将 `QuickToolsView` 类中定义的三个庞大而复杂的方法——`run_system_tool`, `executeTool`, 和 `executeToolWithRecovery`——的完整代码逻辑，全部剪切并迁移到 `QuickToolsLogic` 类中。
    *   **重构视图**: `QuickToolsView` 中的所有工具按钮的点击事件将不再直接执行逻辑，而是统一调用一个 `self.logic.execute_tool(tool_info)` 的入口方法。我将在 `QuickToolsView` 中添加新的槽函数，这些槽函数会连接到 `QuickToolsLogic` 的信号，用于接收执行结果并调用 `InfoBar` 在界面上向用户展示反馈。

## 第二阶段：数据处理与异步管理

### 4. **`[待办]`** 重构`HardwareManager`，下沉数据解析逻辑
*   **实施思路**:
    *   **修改`HardwareManager`**: 我将直接打开并编辑 `app/core/hardware_manager.py` 文件。
    *   **添加格式化方法**: 我会在 `HardwareManager` 类中添加以下六个新的公共方法：`get_formatted_system_info`, `get_formatted_cpu_info`, `get_formatted_motherboard_info`, `get_formatted_memory_info`, `get_formatted_gpu_info`, `get_formatted_disk_info`。这些方法各自负责将传入的原始硬件信息字典中的一部分数据，解析并格式化为一段可以直接在UI上显示的字符串。
    *   **修改信号**: 我将修改 `hardwareInfoReady` 信号的逻辑，使其不再发射原始的、包含复杂嵌套的巨大字典，而是发射一个包含了上述六个方法产出的、已格式化完毕的字符串的新字典。
    *   **重构视图**: 我将重写 `HardwareInfoView.onHardwareInfoReady`（即`loadHardwareInfo`）方法。我会删除其中所有的 `try-except` 块和用于拼接字符串的逻辑。重构后，它将只负责接收 `HardwareManager` 发送过来的已格式化好的字符串，并使用 `setText` 方法将这些字符串直接设置到对应的UI标签上。
    *   **UI错误提示**: 我将修改 `HardwareInfoView.onHardwareInfoError` 槽函数。当此信号被触发时，它将在界面上所有信息卡片的内容区域显示一个明确的错误消息，例如：“获取CPU信息失败，请稍后重试”。

### 5. **`[待办]`** 统一异步任务管理
*   **实施思路**:
    *   **审查 `optimization_view.py`**: 在 `restartExplorerAndDeleteIconCache` 方法中，我将定位到 `thread = threading.Thread(...)` 这行代码。
    *   **重构**: 我会引入 `from app.utils.async_task import AsyncTask`，然后使用 `AsyncTask(self, restart_in_thread).run()` 的方式来包装并执行 `restart_in_thread` 函数，以实现更规范的异步操作。
    *   **审查 `overclocking_view.py` 和 `quick_tools_view.py`**: 我会检查 `command_runner.execute_command` 的每一次调用，找出所有将 `async_mode` 参数设置为 `False` 的地方。
    *   **重构**: 我会将这些调用全部改为异步模式（`async_mode=True`）。同时，我会为 `command_runner` 的 `finished` 信号连接一个用于处理返回结果的回调函数。该回调函数将在命令执行完毕后更新UI，从而彻底避免在启动外部程序时UI线程被阻塞的问题。

## 第三阶段：组件化与代码清理

### 6. **`[待办]`** 拆分`DonateDialog`组件
*   **实施思路**:
    *   **创建目录**: 我将在 `app/` 目录下创建一个新目录 `components`，并在 `components` 内部再创建一个 `dialogs` 目录。
    *   **创建文件**: 我将在 `app/components/dialogs/` 目录下创建一个新文件 `donate_dialog.py`。
    *   **迁移代码**: 我会将 `about_view.py` 文件中定义的 `DonateDialog` 类的完整代码（从 `class DonateDialog(MessageBoxBase):` 开始到其定义结束的全部内容）剪切出来。
    *   **粘贴并修复导入**: 我会将剪切的代码粘贴到新的 `donate_dialog.py` 文件中。然后，我会在该文件的顶部，补全所有运行此类所必需的导入语句，包括 `from PySide6.QtWidgets import ...`, `from qfluentwidgets import ...` 等。
    *   **修改 `about_view.py`**: 我会在 `about_view.py` 文件的顶部，添加一行 `from app.components.dialogs.donate_dialog import DonateDialog`。这样，`onSponsorClicked` 方法中原有的 `dialog = DonateDialog(self)` 代码就可以无需任何其他修改而继续正常工作。

### 7. **`[待办]`** 代码去重和配置外置
*   **实施思路**:
    *   **去重**:
        *   在 `optimization_view.py` 中，我将直接删除 `updatePowerSelectAllCheckbox`、`updateRegistrySelectAllCheckbox` 和 `updateCleanupSelectAllCheckbox` 这三个方法。
        *   接着，在 `initPowerOptionsPage`, `initRegistryOptimizationPage`, `initSystemCleanupPage` 这三个初始化方法中，将所有复选框的 `stateChanged` 信号连接的目标槽函数，统一修改为 `self.updateSelectAllCheckbox`。
        *   我将在 `app_removal_view.py` 文件中执行完全相同的操作，删除 `updateAppSelectAllCheckbox` 和 `updateOnedriveSelectAllCheckbox`，并将信号连接统一到 `self.updateSelectAllCheckbox`。
    *   **配置外置**:
        *   **创建文件**: 我将在 `config/` 目录下创建一个新文件 `app_config.py`。
        *   **添加配置**: 我会在 `app_config.py` 中添加明确的配置变量，例如：
            ```python
            APP_NAME = "Windows 硬件工具箱"
            # from app import __version__
            # APP_VERSION = __version__
            COPYRIGHT = "© 2025 All Rights Reserved"
            AUTHOR_URL = "https://github.com/yourusername"
            QQ_GROUP_URL = "https://example.com/qq-group"
            SOURCE_CODE_URL = "https://github.com/yourusername/WindowsHardwareToolbox"
            ```
        *   **重构 `about_view.py`**: 我会在 `about_view.py` 文件顶部添加 `from config import app_config`。然后，我会将代码中所有硬编码的字符串，包括 `"Windows 硬件工具箱"`, `"https://github.com/yourusername"` 等，全部替换为从 `app_config` 对象中获取对应的属性，例如 `app_config.APP_NAME` 和 `app_config.AUTHOR_URL`。

### 8. **`[待办]`** 最终评审和清理
*   **实施思路**:
    *   我会使用IDE的全局搜索功能，查找所有被注释掉的代码块（以 `#` 开头的行），在确认其功能已被新代码替代且不再需要后，进行彻底删除。
    *   我会逐一检查本次修改过的所有 `.py` 文件，包括所有视图文件、所有新创建的 `logic` 文件和 `component` 文件，仔细检查文件顶部的 `import` 语句，删除所有未被使用的（通常在IDE中会显示为灰色）导入。
    *   我会为所有新创建的公共类（例如 `OptimizationLogic`）和所有非私有的新公共方法（例如 `execute_tasks`）编写符合 PEP 257 规范的文档字符串（docstrings），在文档字符串中清晰地说明其功能、参数和返回值。 


