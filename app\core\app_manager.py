#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
应用操作管理
提供注册表优化、系统清理、应用卸载和OneDrive清理等操作的处理功能
"""

import os
import subprocess
import time
from typing import List, Dict, Any, Callable, Optional, Union, Tuple

from PySide6.QtCore import QObject, Signal

from app.core.command_runner import (
    CommandRunner,
    CommandType,
    CommandResult,
    get_command_runner,
)


class AppManagerError(Exception):
    """应用操作管理器异常基类"""

    pass


class TaskExecutionError(AppManagerError):
    """任务执行异常"""

    def __init__(self, task_name: str, message: str):
        super().__init__(f"执行任务 '{task_name}' 时出错: {message}")
        self.task_name = task_name
        self.message = message


class AppManager(QObject):
    """应用操作管理器"""

    # 信号定义
    taskStarted = Signal(str)  # 任务开始
    taskFinished = Signal(str, bool)  # 任务完成，参数：任务名称，是否成功
    taskProgress = Signal(str, int, int)  # 任务进度，参数：任务名称，当前进度，总进度
    allTasksFinished = Signal(int, int)  # 所有任务完成，参数：成功数，总数

    def __init__(self, parent=None):
        super().__init__(parent)
        self.command_runner = get_command_runner()

        # 连接信号
        self.command_runner.commandStarted.connect(self._on_command_started)
        self.command_runner.commandFinished.connect(self._on_command_finished)
        self.command_runner.commandError.connect(self._on_command_error)

        # 任务统计
        self.current_task = ""
        self.total_tasks = 0
        self.completed_tasks = 0
        self.successful_tasks = 0

    def _on_command_started(self, command: str) -> None:
        """命令开始执行的回调

        Args:
            command: 执行的命令
        """
        pass

    def _on_command_finished(self, result: CommandResult) -> None:
        """命令完成的回调

        Args:
            result: 命令执行结果
        """
        pass

    def _on_command_error(self, error: str) -> None:
        """命令执行错误的回调

        Args:
            error: 错误信息
        """
        pass

    def _reset_statistics(self) -> None:
        """重置任务统计信息"""
        self.current_task = ""
        self.total_tasks = 0
        self.completed_tasks = 0
        self.successful_tasks = 0

    def _update_progress(self, task_name: str, is_success: bool) -> None:
        """更新任务进度

        Args:
            task_name: 任务名称
            is_success: 是否成功
        """
        self.completed_tasks += 1
        if is_success:
            self.successful_tasks += 1

        # 发送任务完成信号
        self.taskFinished.emit(task_name, is_success)

        # 发送进度信号
        self.taskProgress.emit(task_name, self.completed_tasks, self.total_tasks)

        # 检查是否所有任务都已完成
        if self.completed_tasks >= self.total_tasks:
            self.allTasksFinished.emit(self.successful_tasks, self.total_tasks)

    def execute_registry_tasks(self, tasks: List[Tuple[str, List[str]]]) -> None:
        """执行注册表任务列表

        Args:
            tasks: 任务列表，每项为 (任务名称, 命令列表)

        Raises:
            TaskExecutionError: 任务执行失败并且没有成功的备选命令
        """
        if not tasks:
            raise ValueError("任务列表为空")

        self._reset_statistics()
        self.total_tasks = len(tasks)

        for task_name, commands in tasks:
            if not commands:
                self._update_progress(task_name, False)
                continue

            self.current_task = task_name
            self.taskStarted.emit(task_name)

            # 对于每个任务，可能有多个命令，只要有一个成功就算成功
            success = False
            error_messages = []

            for cmd in commands:
                try:
                    result = self.command_runner.execute_command(
                        command=cmd, command_type=CommandType.REGISTRY, admin=True
                    )

                    if result and result.success:
                        success = True
                        break
                    elif result:
                        error_messages.append(f"返回错误: {result.error}")
                except Exception as e:
                    error_messages.append(f"执行异常: {str(e)}")

            self._update_progress(task_name, success)

    def execute_cleanup_tasks(self, tasks: List[Tuple[str, str]]) -> None:
        """执行系统清理任务列表

        Args:
            tasks: 任务列表，每项为 (任务名称, PowerShell命令)

        Raises:
            TaskExecutionError: 任务执行失败
        """
        if not tasks:
            raise ValueError("任务列表为空")

        self._reset_statistics()
        self.total_tasks = len(tasks)

        for task_name, command in tasks:
            self.current_task = task_name
            self.taskStarted.emit(task_name)

            try:
                result = self.command_runner.execute_command(
                    command=command, command_type=CommandType.POWERSHELL, admin=True
                )

                if not result:
                    raise TaskExecutionError(task_name, "命令执行器返回空结果")

                self._update_progress(task_name, result.success)

                if not result.success:
                    print(f"清理任务 '{task_name}' 失败: {result.error}")

            except Exception as e:
                self._update_progress(task_name, False)
                print(f"执行清理任务 '{task_name}' 时出现异常: {str(e)}")

    def execute_appx_uninstall(self, packages: List[str]) -> None:
        """执行应用卸载任务

        Args:
            packages: 要卸载的应用包名称列表

        Raises:
            ValueError: 包列表为空
        """
        if not packages:
            raise ValueError("应用包列表为空")

        self._reset_statistics()
        self.total_tasks = len(packages)

        for package in packages:
            task_name = f"卸载 {package}"
            self.current_task = task_name
            self.taskStarted.emit(task_name)

            try:
                # 构建卸载命令 - 处理通配符并卸载已安装和预装版本
                cmd = f"""
                # 卸载当前用户安装的应用
                Get-AppxPackage *{package}* | Remove-AppxPackage -ErrorAction SilentlyContinue
                
                # 卸载所有用户安装的应用
                Get-AppxPackage -AllUsers *{package}* | Remove-AppxPackage -ErrorAction SilentlyContinue
                
                # 卸载预装版本
                Get-AppxProvisionedPackage -Online | Where-Object {{$_.DisplayName -like "*{package}*"}} | Remove-AppxProvisionedPackage -Online -ErrorAction SilentlyContinue
                """

                result = self.command_runner.execute_command(
                    command=cmd, command_type=CommandType.POWERSHELL, admin=True
                )

                if not result:
                    raise TaskExecutionError(task_name, "命令执行器返回空结果")

                self._update_progress(task_name, result.success)

                if not result.success:
                    print(f"应用卸载任务 '{task_name}' 失败: {result.error}")

            except Exception as e:
                self._update_progress(task_name, False)
                print(f"执行应用卸载任务 '{task_name}' 时出现异常: {str(e)}")

    def execute_onedrive_cleanup(
        self, tasks: List[Tuple[str, Union[str, List[str]]]]
    ) -> None:
        """执行OneDrive清理任务

        Args:
            tasks: 任务列表，每项为 (任务名称, PowerShell命令) 或 (任务名称, PowerShell命令列表)

        Raises:
            ValueError: 任务列表为空
        """
        if not tasks:
            raise ValueError("任务列表为空")

        self._reset_statistics()
        self.total_tasks = len(tasks)

        for task in tasks:
            task_name = task[0]
            commands = task[1] if isinstance(task[1], list) else [task[1]]

            if not commands:
                self._update_progress(task_name, False)
                continue

            self.current_task = task_name
            self.taskStarted.emit(task_name)

            # 对于有多个命令的任务，只要有一个成功就算成功
            success = False
            error_messages = []

            for cmd in commands:
                try:
                    result = self.command_runner.execute_command(
                        command=cmd, command_type=CommandType.POWERSHELL, admin=True
                    )

                    if result and result.success:
                        success = True
                        break
                    elif result:
                        error_messages.append(f"返回错误: {result.error}")
                except Exception as e:
                    error_messages.append(f"执行异常: {str(e)}")

            self._update_progress(task_name, success)

            if not success and error_messages:
                print(
                    f"OneDrive清理任务 '{task_name}' 失败: {'; '.join(error_messages)}"
                )

    def restart_explorer(self) -> bool:
        """重启资源管理器

        Returns:
            bool: 是否成功
        """
        try:
            # 终止资源管理器
            kill_cmd = "taskkill /f /im explorer.exe"
            kill_result = self.command_runner.execute_command(
                command=kill_cmd, command_type=CommandType.CMD
            )

            # 等待一秒
            time.sleep(1)

            # 重启资源管理器
            start_cmd = "start explorer.exe"
            start_result = self.command_runner.execute_command(
                command=start_cmd, command_type=CommandType.CMD
            )

            return (kill_result and kill_result.success) and (
                start_result and start_result.success
            )
        except Exception as e:
            print(f"重启资源管理器失败: {str(e)}")
            return False

    def delete_icon_cache(self) -> bool:
        """删除图标缓存

        Returns:
            bool: 是否成功
        """
        try:
            # 构建删除命令
            cmd = """
            # 终止资源管理器
            Stop-Process -Name "explorer" -Force -ErrorAction SilentlyContinue
            
            # 等待进程终止
            Start-Sleep -Seconds 1
            
            # 删除图标缓存文件
            Remove-Item -Path "$env:LOCALAPPDATA\\Microsoft\\Windows\\Explorer\\iconcache*" -Force -ErrorAction SilentlyContinue
            Remove-Item -Path "$env:LOCALAPPDATA\\IconCache.db" -Force -ErrorAction SilentlyContinue
            
            # 启动资源管理器
            Start-Process "explorer.exe"
            """

            result = self.command_runner.execute_command(
                command=cmd, command_type=CommandType.POWERSHELL, admin=True
            )

            return result and result.success
        except Exception as e:
            print(f"删除图标缓存失败: {str(e)}")
            return False

    def execute_power_option_unlock(self) -> bool:
        """解锁电源高级选项

        Returns:
            bool: 是否成功
        """
        try:
            from config.powershell_commands import unlock_power_options

            result = self.command_runner.execute_command(
                command=unlock_power_options,
                command_type=CommandType.POWERSHELL,
                admin=True,
            )

            return result and result.success
        except ImportError as e:
            print(f"加载电源选项脚本失败: {str(e)}")
            return False
        except Exception as e:
            print(f"解锁电源高级选项失败: {str(e)}")
            return False

    def execute_quick_tool(self, command: str, admin: bool = False) -> bool:
        """执行快捷工具命令

        Args:
            command: 要执行的命令
            admin: 是否需要管理员权限

        Returns:
            bool: 是否成功
        """
        if not command:
            print("命令为空")
            return False

        try:
            # 确定命令类型
            command_type = CommandType.CMD
            if command.startswith("powershell") or command.endswith(".ps1"):
                command_type = CommandType.POWERSHELL
            elif command.endswith(".exe"):
                command_type = CommandType.EXECUTABLE

            result = self.command_runner.execute_command(
                command=command, command_type=command_type, admin=admin
            )

            return result and result.success
        except Exception as e:
            print(f"执行快捷工具命令失败: {str(e)}")
            return False

    def enter_bios(self) -> bool:
        """一键进入BIOS

        Returns:
            bool: 是否成功
        """
        try:
            # 构建命令
            cmd = """
            # 设置下次启动进入固件设置
            shutdown /r /fw /t 0
            """

            result = self.command_runner.execute_command(
                command=cmd, command_type=CommandType.CMD, admin=True
            )

            return result and result.success
        except Exception as e:
            print(f"设置进入BIOS失败: {str(e)}")
            return False


# 线程安全的单例模式
import threading

_app_manager_instance = None
_app_manager_lock = threading.Lock()


def get_app_manager() -> AppManager:
    """获取AppManager实例(线程安全的单例模式)

    Returns:
        AppManager: 应用管理器实例
    """
    global _app_manager_instance
    if _app_manager_instance is None:
        with _app_manager_lock:
            # 双重检查锁定模式
            if _app_manager_instance is None:
                _app_manager_instance = AppManager()
    return _app_manager_instance
