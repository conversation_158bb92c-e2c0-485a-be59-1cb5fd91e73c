#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通用组件模块
提供可在多个视图中复用的组件和方法
"""

from typing import List, Tuple, Any
from PySide6.QtCore import Qt
from qfluentwidgets import CheckBox


def set_checkboxes_state(
    checkboxes_list: List[List[Tuple[Any, CheckBox]]], is_checked: bool
) -> None:
    """设置所有复选框的状态
    
    批量设置多个复选框组的选中状态，用于实现全选/取消全选功能。
    处理过程中会暂时阻断信号以防止触发不必要的事件。

    Args:
        checkboxes_list: 复选框列表的列表，每个元素是一个列表，包含(key, checkbox)元组
        is_checked: 是否选中
    """
    # 合并所有复选框列表
    all_checkboxes = []
    for checkboxes in checkboxes_list:
        all_checkboxes.extend(checkboxes)

    # 设置所有复选框状态
    for _, checkbox in all_checkboxes:
        checkbox.blockSignals(True)
        checkbox.setChecked(is_checked)
        checkbox.blockSignals(False)


def update_selectall_state(
    select_all_checkbox: CheckBox, checkboxes_list: List[List[Tuple[Any, CheckBox]]]
) -> None:
    """更新全选复选框状态
    
    根据子复选框的选中状态，更新全选复选框的状态（选中/未选中/部分选中）。
    用于在用户手动选择/取消选择单个选项时，自动更新全选框状态。

    Args:
        select_all_checkbox: 全选复选框
        checkboxes_list: 复选框列表的列表，每个元素是一个列表，包含(key, checkbox)元组
    """
    # 暂时阻断信号以防止递归调用
    select_all_checkbox.blockSignals(True)

    # 合并所有复选框列表
    all_checkboxes = []
    for checkboxes in checkboxes_list:
        all_checkboxes.extend(checkboxes)

    all_checked = True
    any_checked = False

    for key, checkbox in all_checkboxes:
        if checkbox.isChecked():
            any_checked = True
        else:
            all_checked = False

    # 更新全选复选框状态
    if all_checked:
        select_all_checkbox.setChecked(True)
    elif not any_checked:
        select_all_checkbox.setChecked(False)
    else:
        # 部分选中状态
        select_all_checkbox.setCheckState(Qt.CheckState.PartiallyChecked)

    # 恢复信号连接
    select_all_checkbox.blockSignals(False)
