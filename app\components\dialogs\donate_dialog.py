#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
赞助对话框组件
提供支付宝和微信二维码显示功能
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QLabel, QFrame, QVBoxLayout, QStackedWidget
from PySide6.QtGui import QPixmap
from qfluentwidgets import (
    MessageBoxBase,
    SubtitleLabel,
    BodyLabel,
    SegmentedWidget,
)
import os


class DonateDialog(MessageBoxBase):
    """赞助对话框，用于显示支付宝和微信二维码

    继承自MessageBoxBase的对话框，使用SegmentedWidget提供支付宝和微信支付
    两种捐赠方式的切换功能，并显示相应的二维码图片。

    Attributes:
        titleLabel: 对话框标题标签
        descriptionLabel: 对话框描述文字
        segmentedWidget: 分段控件，用于切换支付方式
        stackedWidget: 堆叠小部件，包含支付宝和微信支付页面
        alipayWidget: 支付宝支付页面
        wechatWidget: 微信支付页面
    """

    def __init__(self, parent=None):
        """初始化赞助对话框

        创建并设置对话框UI元素，包括标题、描述、支付方式选择器和支付二维码显示。

        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.titleLabel = SubtitleLabel("赞助作者", self)
        self.setMinimumWidth(500)
        self.setMinimumHeight(450)

        # 添加标题到布局
        self.viewLayout.addWidget(self.titleLabel)

        # 添加说明文字
        self.descriptionLabel = BodyLabel(
            "感谢您对本项目的支持！选择支付方式后扫描二维码进行赞助。", self
        )
        self.descriptionLabel.setWordWrap(True)
        self.viewLayout.addWidget(self.descriptionLabel)

        # 创建堆叠小部件来存放支付页面
        self.stackedWidget = QStackedWidget(self)
        self.alipayWidget = self._create_alipay_widget()
        self.wechatWidget = self._create_wechat_widget()

        # 添加支付页面到堆叠小部件
        self.stackedWidget.addWidget(self.alipayWidget)
        self.stackedWidget.addWidget(self.wechatWidget)

        # 创建分段控件作为选项卡
        self.segmentedWidget = SegmentedWidget(self)

        # 添加选项卡项
        self.segmentedWidget.addItem(
            routeKey="alipay",
            text="支付宝",
            onClick=lambda: self.stackedWidget.setCurrentWidget(self.alipayWidget),
        )
        self.segmentedWidget.addItem(
            routeKey="wechat",
            text="微信支付",
            onClick=lambda: self.stackedWidget.setCurrentWidget(self.wechatWidget),
        )

        # 设置默认显示的选项卡
        self.segmentedWidget.setCurrentItem("alipay")
        self.stackedWidget.setCurrentWidget(self.alipayWidget)

        # 将选项卡和内容添加到布局
        self.viewLayout.addWidget(self.segmentedWidget, alignment=Qt.AlignCenter)
        self.viewLayout.addWidget(self.stackedWidget)

    def _create_alipay_widget(self):
        """创建支付宝二维码控件

        创建包含支付宝二维码图片的页面。

        Returns:
            QFrame: 包含支付宝二维码的框架控件
        """
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        layout.setAlignment(Qt.AlignCenter)

        # 创建支付宝二维码图片
        qrcode_label = QLabel(widget)
        qrcode_path = "assets/alipay.png"

        if os.path.exists(qrcode_path):
            pixmap = QPixmap(qrcode_path)
            if not pixmap.isNull():
                pixmap = pixmap.scaled(
                    300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation
                )
                qrcode_label.setPixmap(pixmap)
                qrcode_label.setAlignment(Qt.AlignCenter)
        else:
            # 二维码不存在时显示提示
            qrcode_label.setText("二维码图片不存在")
            qrcode_label.setAlignment(Qt.AlignCenter)

        # 添加标题和二维码到布局
        title_label = SubtitleLabel("支付宝扫码赞助", widget)
        title_label.setAlignment(Qt.AlignCenter)

        layout.addWidget(title_label)
        layout.addWidget(qrcode_label)

        return widget

    def _create_wechat_widget(self):
        """创建微信二维码控件

        创建包含微信支付二维码图片的页面。

        Returns:
            QFrame: 包含微信支付二维码的框架控件
        """
        widget = QFrame()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        layout.setAlignment(Qt.AlignCenter)

        # 创建微信二维码图片
        qrcode_label = QLabel(widget)
        qrcode_path = "assets/wechat_pay.png"

        if os.path.exists(qrcode_path):
            pixmap = QPixmap(qrcode_path)
            if not pixmap.isNull():
                pixmap = pixmap.scaled(
                    300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation
                )
                qrcode_label.setPixmap(pixmap)
                qrcode_label.setAlignment(Qt.AlignCenter)
        else:
            # 二维码不存在时显示提示
            qrcode_label.setText("二维码图片不存在")
            qrcode_label.setAlignment(Qt.AlignCenter)

        # 添加标题和二维码到布局
        title_label = SubtitleLabel("微信扫码赞助", widget)
        title_label.setAlignment(Qt.AlignCenter)

        layout.addWidget(title_label)
        layout.addWidget(qrcode_label)

        return widget
