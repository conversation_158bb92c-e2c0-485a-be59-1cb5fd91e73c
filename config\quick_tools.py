#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 快捷工具配置
# 此配置文件包含所有可在快捷工具视图中显示的工具

QUICK_TOOLS = [
    {
        "category": "系统管理工具",
        "icon": "POWER_BUTTON",
        "tools": [
            {
                "name": "控制面板",
                "icon": "SETTING",
                "command": "control",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "程序和功能",
                "icon": "APPLICATION",
                "command": "appwiz.cpl",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "上帝模式",
                "icon": "MENU",
                "command": "shell:::{ED7BA470-8E54-465E-825C-99712043E01C}",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "设备管理器",
                "icon": "IOT",
                "command": "devmgmt.msc",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "磁盘管理",
                "icon": "SAVE",
                "command": "diskmgmt.msc",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "任务管理器",
                "icon": "APPLICATION",
                "command": "taskmgr",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "组策略编辑器",
                "icon": "EDIT",
                "command": "gpedit.msc",
                "require_confirm": False,
                "require_admin": True,
            },
            {
                "name": "本地安全策略",
                "icon": "CERTIFICATE",
                "command": "secpol.msc",
                "require_confirm": False,
                "require_admin": True,
            },
            {
                "name": "服务管理",
                "icon": "SETTING",
                "command": "services.msc",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "计算机管理",
                "icon": "SETTING",
                "command": "compmgmt.msc",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "注册表编辑器",
                "icon": "EDIT",
                "command": "regedit",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "系统配置",
                "icon": "SETTING",
                "command": "msconfig",
                "require_confirm": False,
                "require_admin": True,
            },
        ],
    },
    {
        "category": "系统设置",
        "icon": "SETTING",
        "tools": [
            {
                "name": "Windows设置",
                "icon": "SETTING",
                "command": "start ms-settings:",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "网络设置",
                "icon": "WIFI",
                "command": "start ms-settings:network",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "蓝牙设置",
                "icon": "BLUETOOTH",
                "command": "start ms-settings:bluetooth",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "电源选项",
                "icon": "POWER_BUTTON",
                "command": "powercfg.cpl",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "性能选项",
                "icon": "SPEED_HIGH",
                "command": "SystemPropertiesPerformance",
                "require_confirm": False,
                "require_admin": True,
            },
            {
                "name": "桌面图标设置",
                "icon": "SETTING",
                "command": "rundll32.exe shell32.dll,Control_RunDLL desk.cpl,,0",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "Windows防火墙",
                "icon": "CANCEL",
                "command": "firewall.cpl",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "Internet选项",
                "icon": "GLOBE",
                "command": "inetcpl.cpl",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "网络连接",
                "icon": "WIFI",
                "command": "ncpa.cpl",
                "require_confirm": False,
                "require_admin": False,
            },
        ],
    },
    {
        "category": "诊断和监控",
        "icon": "VIEW",
        "tools": [
            {
                "name": "DirectX诊断工具",
                "icon": "GAME",
                "command": "dxdiag",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "资源监视器",
                "icon": "VIEW",
                "command": "resmon",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "性能监视器",
                "icon": "SPEED_HIGH",
                "command": "perfmon",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "系统信息",
                "icon": "INFO",
                "command": "msinfo32",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "事件查看器",
                "icon": "DOCUMENT",
                "command": "eventvwr.msc",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "证书管理",
                "icon": "CERTIFICATE",
                "command": "certmgr.msc",
                "require_confirm": False,
                "require_admin": False,
            },
        ],
    },
    {
        "category": "系统维护",
        "icon": "UPDATE",
        "tools": [
            {
                "name": "命令提示符",
                "icon": "COMMAND_PROMPT",
                "command": "cmd",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "PowerShell",
                "icon": "CODE",
                "command": "powershell",
                "require_confirm": False,
                "require_admin": False,
            },
            {
                "name": "安全模式",
                "icon": "CERTIFICATE",
                "command": "bcdedit /set {current} safeboot minimal",
                "require_confirm": True,
                "require_admin": True,
                "description": "重启计算机后将进入安全模式，完成后自动恢复正常启动",
            },
        ],
    },
    {
        "category": "电源控制",
        "icon": "POWER_BUTTON",
        "tools": [
            {
                "name": "重启",
                "icon": "SYNC",
                "command": "shutdown /r /t 0",
                "require_confirm": True,
                "require_admin": False,
            },
            {
                "name": "关机",
                "icon": "POWER_BUTTON",
                "command": "shutdown /s /t 0",
                "require_confirm": True,
                "require_admin": False,
            },
            {
                "name": "睡眠",
                "icon": "FRIGID",
                "command": "rundll32.exe powrprof.dll,SetSuspendState 0,1,0",
                "require_confirm": True,
                "require_admin": False,
            },
            {
                "name": "锁定计算机",
                "icon": "PIN",
                "command": "rundll32.exe user32.dll,LockWorkStation",
                "require_confirm": True,
                "require_admin": False,
            },
        ],
    },
]
