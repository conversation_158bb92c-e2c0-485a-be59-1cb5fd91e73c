#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图标提取工具
提供从可执行文件中提取图标的功能

此模块主要用于从Windows可执行文件(.exe)中提取不同尺寸的图标，
支持获取最佳尺寸的图标和图标的尺寸信息，为应用程序提供视觉展示。
"""

import os
from typing import List, Optional, Dict, Union
from PySide6.QtCore import QFileInfo, QSize
from PySide6.QtGui import QIcon, QPixmap, QImage
from PySide6.QtWidgets import QFileIconProvider


class IconExtractor:
    """图标提取器

    提供从文件中提取图标的功能，特别是Windows可执行文件(.exe)
    支持获取最佳尺寸的图标和图标的尺寸信息
    """

    def __init__(self):
        """初始化图标提取器

        创建文件图标提供器实例
        """
        self._icon_provider = QFileIconProvider()

    def get_file_icon(self, file_path: str) -> QIcon:
        """获取文件图标

        Args:
            file_path: 文件路径

        Returns:
            QIcon: 文件的图标对象

        Note:
            如果文件不存在或路径无效，将返回空图标
        """
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return QIcon()

        # 创建QFileInfo对象
        file_info = QFileInfo(file_path)

        # 通过QFileIconProvider获取图标
        return self._icon_provider.icon(file_info)

    def get_icon_sizes(self, icon: QIcon) -> List[QSize]:
        """获取图标的可用尺寸列表

        Args:
            icon: 要检查的图标对象

        Returns:
            List[QSize]: 图标可用尺寸的列表
        """
        if icon.isNull():
            return []

        return icon.availableSizes()

    def get_best_icon_pixmap(
        self, icon: QIcon, requested_size: QSize = QSize(64, 64)
    ) -> QPixmap:
        """获取最佳图标像素图

        Args:
            icon: 图标对象
            requested_size: 请求的图标尺寸，默认为64x64

        Returns:
            QPixmap: 最佳图标像素图

        Algorithm:
            1. 检查图标是否为空，如果为空返回空QPixmap
            2. 获取图标的所有可用尺寸
            3. 如果有可用尺寸，选择宽度不大于请求尺寸且最接近请求尺寸的尺寸
            4. 如果没有合适的尺寸，则使用请求的尺寸
            5. 返回所选尺寸的图标像素图
        """
        if icon.isNull():
            return QPixmap()

        # 获取图标的可用尺寸列表
        sizes = icon.availableSizes()

        if sizes:
            # 找到接近请求尺寸的最大尺寸
            best_size = max(
                sizes,
                key=lambda s: s.width() if s.width() <= requested_size.width() else 0,
            )

            # 如果找到了合适的尺寸，使用它；否则使用请求的尺寸
            use_size = best_size if best_size.width() > 0 else requested_size
            return icon.pixmap(use_size)

        # 如果图标没有可用尺寸，使用请求的尺寸
        return icon.pixmap(requested_size)

    def get_icon_info(self, file_path: str) -> Dict[str, Union[bool, str, List[str]]]:
        """获取文件图标的详细信息

        Args:
            file_path: 文件路径

        Returns:
            Dict: 包含图标是否有效、图标尺寸信息的字典

        Example:
            返回格式示例:
            {
                'valid': True,
                'path': 'C:/path/to/file.exe',
                'sizes': ['16x16', '32x32', '48x48', '64x64']
            }
        """
        icon = self.get_file_icon(file_path)
        sizes = self.get_icon_sizes(icon)

        return {
            "valid": not icon.isNull(),
            "path": file_path,
            "sizes": [f"{size.width()}x{size.height()}" for size in sizes],
        }


# 单例模式
_icon_extractor_instance = None


def get_icon_extractor() -> IconExtractor:
    """获取IconExtractor实例(单例模式)

    确保整个应用程序中只有一个IconExtractor实例

    Returns:
        IconExtractor: 图标提取器实例
    """
    global _icon_extractor_instance
    if _icon_extractor_instance is None:
        _icon_extractor_instance = IconExtractor()
    return _icon_extractor_instance
