#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快捷工具逻辑模块
提供QuickToolsView的业务逻辑处理
"""

import os
import subprocess
import tempfile
import sys
from typing import Dict, Any, Optional, Callable

from PySide6.QtCore import QObject, Signal

from app.core.command_runner import get_command_runner, CommandType, CommandResult


class QuickToolsLogic(QObject):
    """快捷工具逻辑处理器

    负责处理快捷工具的执行逻辑，包括普通工具、系统工具和需要恢复的工具（如安全模式）。
    提供统一的命令执行接口，并处理各种特殊情况如管理员权限要求和确认提示。

    Signals:
        commandFinished (str, bool): 命令执行完成信号，参数为命令名称和是否成功
        infoMessage (str, str): 信息消息信号，参数为标题和内容

    Attributes:
        CONFIRM_TOOLS (list): 需要确认的工具列表，主要是有风险的操作如重启、关机等
        ADMIN_TOOLS (list): 需要管理员权限的工具列表
    """

    # 信号定义
    commandFinished = Signal(str, bool)  # 命令执行完成，参数：命令名称，是否成功
    infoMessage = Signal(str, str)  # 信息消息，参数：标题，内容

    # 需要确认的工具列表
    CONFIRM_TOOLS = [
        "安全模式重启",
        "安全模式",
        "重新启动",
        "重启",
        "关机",
        "睡眠",
        "锁定计算机",
        "锁定",
    ]

    # 需要管理员权限的工具列表
    ADMIN_TOOLS = [
        "安全模式重启",
        "安全模式",
        "组策略编辑器",
        "本地安全策略",
        "性能选项",
    ]

    # 使用Start命令不应该显示控制台窗口的工具列表
    GUI_TOOLS = [
        "控制面板",
        "程序和功能",
        "Windows防火墙",
        "Internet选项",
        "电源选项",
        "性能监视器",
        "资源监视器",
        "事件查看器",
        "DirectX诊断工具",
        "计算机管理",
        "服务管理",
        "证书管理",
    ]

    def __init__(self, parent=None):
        """初始化快捷工具逻辑处理器

        Args:
            parent: 父对象，通常是QuickToolsView实例
        """
        super().__init__(parent)
        self.command_runner = get_command_runner()

    def execute_tool(
        self, name: str, command: str, require_admin: bool = False
    ) -> bool:
        """执行工具命令

        根据命令类型和工具需求，使用适当的方式执行命令，
        并通过信号机制通知UI层执行结果。

        Args:
            name: 工具名称
            command: 要执行的命令
            require_admin: 是否需要管理员权限

        Returns:
            bool: 执行是否成功

        Raises:
            Exception: 执行过程中的异常会被捕获并通过infoMessage信号通知
        """
        try:
            # 检查命令是否为空
            if not command:
                self.infoMessage.emit("命令错误", f"{name} 没有指定有效的命令")
                return False

            # 判断命令类型和执行方式
            if (
                command.lower().startswith("start ")
                or command.lower().endswith(".msc")
                or command.lower().endswith(".cpl")
                or "shell:::" in command.lower()
                or command.lower() == "cmd"
                or command.lower() == "powershell"
                or "ms-settings:" in command
            ):
                success = self.run_system_tool(command, require_admin, name)
                # 系统工具执行完成后，无论成功与否都需要发送信号通知UI
                # 因为run_system_tool内部已经处理了成功/失败的UI通知
                self.commandFinished.emit(name, success)
                return success

            # 执行其他类型命令
            self.infoMessage.emit("执行中", f"正在执行: {name}")

            # 确定命令类型
            command_type = CommandType.CMD  # 默认为CMD命令
            if command.lower().startswith("powershell") or name.lower() == "powershell":
                command_type = CommandType.POWERSHELL
            elif command.lower().endswith(".ps1"):
                command_type = CommandType.POWERSHELL
            elif command.lower().endswith(".exe"):
                command_type = CommandType.EXECUTABLE

            # 同步执行命令，避免线程问题
            result = self.command_runner.execute_command(
                command=command,
                command_type=command_type,
                admin=require_admin,
                async_mode=False,  # 同步执行，避免线程问题
            )

            # 处理结果
            # 特别注意：某些系统工具即使启动成功也可能返回非零退出码
            # 只要没有明确的错误信息，我们就认为它启动成功了
            success = False
            if result:
                if not result.success and not result.error:
                    # 如果有非零退出码但没有错误信息，仍然视为成功
                    success = True
                else:
                    success = result.success

            self.commandFinished.emit(name, success)

            if not success and result and hasattr(result, "error") and result.error:
                error_msg = result.error
                self.infoMessage.emit("执行失败", f"{name} 执行失败: {error_msg}")

            return success

        except Exception as e:
            self.infoMessage.emit("执行失败", f"执行 {name} 时出错: {str(e)}")
            self.commandFinished.emit(name, False)
            return False

    def execute_tool_with_recovery(
        self, name: str, command: str, require_admin: bool = False
    ) -> bool:
        """执行带恢复命令的工具（主要用于安全模式）

        执行可能需要系统级别恢复的特殊工具，如安全模式启动。
        会创建自动恢复任务，确保系统能够在下次启动时恢复正常状态。

        Args:
            name: 工具名称
            command: 要执行的命令
            require_admin: 是否需要管理员权限

        Returns:
            bool: 执行是否成功

        Raises:
            Exception: 执行过程中的异常会被捕获并通过infoMessage信号通知
        """
        try:
            # 1. 设置安全模式
            success = self.run_system_tool(command, as_admin=True, tool_name=name)

            if success:
                # 2. 创建启动任务，在下次启动时删除安全模式设置
                try:
                    # 创建临时批处理文件
                    temp_dir = tempfile.gettempdir()
                    batch_file = os.path.join(temp_dir, "disable_safemode.bat")

                    with open(batch_file, "w") as f:
                        f.write("@echo off\n")
                        f.write("bcdedit /deletevalue {current} safeboot\n")
                        f.write('schtasks /delete /tn "DisableSafeMode" /f\n')
                        f.write('del "%~f0"\n')  # 自删除批处理文件

                    # 创建计划任务，在下次启动时运行批处理文件
                    task_cmd = f'schtasks /create /tn "DisableSafeMode" /sc onstart /ru SYSTEM /delay 0000:30 /tr "{batch_file}" /f'
                    recovery_success = self.run_system_tool(
                        task_cmd, as_admin=True, tool_name="创建安全模式恢复任务"
                    )

                    if recovery_success:
                        # 显示成功信息
                        self.infoMessage.emit(
                            "安全模式设置成功",
                            "已创建自动恢复任务，系统将在下次启动后自动退出安全模式",
                        )
                    else:
                        # 显示警告
                        self.infoMessage.emit(
                            "自动恢复任务创建失败",
                            "请在安全模式中手动执行 'bcdedit /deletevalue {current} safeboot' 命令恢复正常启动",
                        )
                except Exception as e:
                    # 创建恢复任务失败
                    self.infoMessage.emit(
                        "自动恢复任务创建失败",
                        f"请在安全模式中手动执行 'bcdedit /deletevalue {{current}} safeboot' 命令恢复正常启动: {str(e)}",
                    )

                return True

            return False

        except Exception as e:
            self.infoMessage.emit("执行失败", f"执行 {name} 时出错: {str(e)}")
            return False

    def run_system_tool(
        self, command: str, as_admin: bool = False, tool_name: str = None
    ) -> bool:
        """运行Windows系统工具

        专门处理Windows系统工具的启动，如控制面板项、设置页面、
        MSC文件等，根据不同的工具类型使用适当的启动方式。

        Args:
            command: 要执行的系统命令（如msc文件、控制面板项等）
            as_admin: 是否需要管理员权限
            tool_name: 工具的显示名称，用于日志记录

        Returns:
            bool: 是否成功启动工具

        Raises:
            Exception: 执行过程中的异常会被捕获并通过infoMessage信号通知
        """
        try:
            # 显示启动提示
            if tool_name:
                self.infoMessage.emit("启动中", f"正在启动 {tool_name}...")

            if sys.platform == "win32":
                # 特殊处理ms-settings链接
                if command.startswith("ms-settings:"):
                    # 使用Windows内置的方式打开设置
                    os.startfile(command)
                    success = True
                # 特殊处理shell协议
                elif "shell:::" in command:
                    os.system(f"explorer {command}")
                    success = True
                # 对于特殊命令使用不同处理方式
                elif as_admin:
                    # 需要管理员权限时使用命令运行器
                    # 但使用同步模式避免线程问题
                    cmd_type = CommandType.CMD
                    if (
                        command.lower().endswith(".exe")
                        or command.lower() == "cmd"
                        or command.lower() == "powershell"
                    ):
                        cmd_type = CommandType.EXECUTABLE
                    elif command.lower().endswith(".ps1") or command.lower().startswith(
                        "powershell"
                    ):
                        cmd_type = CommandType.POWERSHELL

                    result = self.command_runner.execute_command(
                        command=command,
                        command_type=cmd_type,
                        admin=as_admin,
                        async_mode=False,
                    )

                    # 特别注意：某些系统工具即使启动成功也可能返回非零退出码
                    # 只要没有明确的错误信息，我们就认为它启动成功了
                    if result:
                        if not result.success and not result.error:
                            # 如果有非零退出码但没有错误信息，仍然视为成功
                            success = True
                        else:
                            success = result.success
                            if not success:
                                error_msg = result.error
                    else:
                        success = False
                else:
                    # 使用subprocess直接启动，避免线程问题
                    try:
                        # 设置启动信息以隐藏控制台窗口
                        startupinfo = None
                        if hasattr(subprocess, "STARTUPINFO"):
                            startupinfo = subprocess.STARTUPINFO()
                            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                            startupinfo.wShowWindow = subprocess.SW_HIDE

                        # 直接使用os.startfile打开MSC和CPL文件
                        if command.lower().endswith(".msc") or command.lower().endswith(
                            ".cpl"
                        ):
                            os.startfile(command)
                            success = True
                        # 对于start命令，使用简化的方式启动
                        elif command.lower().startswith("start "):
                            # 使用os.system直接执行，这是简单且可靠的方式
                            ret_code = os.system(command)
                            # 注意：os.system返回的是退出状态左移8位的值，实际退出码需要右移8位
                            exit_code = ret_code >> 8 if ret_code > 255 else ret_code

                            # 对于start命令，即使返回非零退出码通常也意味着程序已启动
                            # 因为start命令本身结束了，但启动的程序仍在运行
                            success = True

                            # 只有在明显错误的情况下才报告失败
                            if exit_code > 2:
                                print(
                                    f"Command exited with code {exit_code}: {command}"
                                )
                                if exit_code == 9009:  # 命令不存在
                                    success = False
                                    error_msg = f"找不到命令或程序: {command}"
                        else:
                            # 对于其他命令，使用subprocess以隐藏控制台窗口
                            process = subprocess.Popen(
                                command,
                                shell=True,
                                startupinfo=startupinfo,
                                creationflags=subprocess.CREATE_NO_WINDOW
                                | subprocess.DETACHED_PROCESS,
                            )
                            # 不等待进程结束，认为启动成功
                            success = True
                    except Exception as e:
                        print(f"启动命令失败: {e}")
                        success = False
                        error_msg = str(e)

                # 显示结果
                if success:
                    # 显示成功提示
                    if tool_name:
                        self.infoMessage.emit("启动成功", f"{tool_name} 已启动")
                    return True
                else:
                    # 显示错误
                    if tool_name:
                        error_msg = error_msg if "error_msg" in locals() else "未知错误"
                        self.infoMessage.emit(
                            "启动失败", f"启动 {tool_name} 失败: {error_msg}"
                        )
                    return False
            else:
                # 非Windows系统
                self.infoMessage.emit("不支持", "此功能仅支持Windows系统")
                return False

        except Exception as e:
            # 显示错误提示
            if tool_name:
                self.infoMessage.emit("启动失败", f"启动 {tool_name} 时出错: {str(e)}")
            return False
